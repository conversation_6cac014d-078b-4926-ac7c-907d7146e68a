{"version": 3, "sources": ["../../src/client/web-vitals.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport {\n  onLCP,\n  onFID,\n  onCLS,\n  onINP,\n  onFCP,\n  onTTFB,\n} from 'next/dist/compiled/web-vitals'\nimport type { Metric } from 'next/dist/compiled/web-vitals'\n\nexport function useReportWebVitals(\n  reportWebVitalsFn: (metric: Metric) => void\n) {\n  useEffect(() => {\n    onCLS(reportWebVitalsFn)\n    onFID(reportWebVitalsFn)\n    onLCP(reportWebVitalsFn)\n    onINP(reportWebVitalsFn)\n    onFCP(reportWebVitalsFn)\n    onTTFB(reportWebVitalsFn)\n  }, [reportWebVitalsFn])\n}\n"], "names": ["useEffect", "onLCP", "onFID", "onCLS", "onINP", "onFCP", "onTTFB", "useReportWebVitals", "reportWebVitalsFn"], "mappings": "AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SACEC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,MAAM,QACD,gCAA+B;AAGtC,OAAO,SAASC,mBACdC,iBAA2C;IAE3CR,UAAU;QACRG,MAAMK;QACNN,MAAMM;QACNP,MAAMO;QACNJ,MAAMI;QACNH,MAAMG;QACNF,OAAOE;IACT,GAAG;QAACA;KAAkB;AACxB"}
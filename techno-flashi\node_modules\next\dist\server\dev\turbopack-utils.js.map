{"version": 3, "sources": ["../../../src/server/dev/turbopack-utils.ts"], "sourcesContent": ["import type {\n  ServerFields,\n  SetupOpts,\n} from '../lib/router-utils/setup-dev-bundler'\nimport type {\n  Issue,\n  TurbopackResult,\n  Endpoint,\n  RawEntrypoints,\n  Update as TurbopackUpdate,\n  WrittenEndpoint,\n} from '../../build/swc/types'\nimport {\n  type HMR_ACTION_TYPES,\n  HMR_ACTIONS_SENT_TO_BROWSER,\n} from './hot-reloader-types'\nimport * as Log from '../../build/output/log'\nimport type { PropagateToWorkersField } from '../lib/router-utils/types'\nimport type { TurbopackManifestLoader } from '../../shared/lib/turbopack/manifest-loader'\nimport type { AppRoute, Entrypoints, PageRoute } from '../../build/swc/types'\nimport {\n  type EntryKey,\n  getEntryKey,\n  splitEntryKey,\n} from '../../shared/lib/turbopack/entry-key'\nimport type ws from 'next/dist/compiled/ws'\nimport { isMetadataRoute } from '../../lib/metadata/is-metadata-route'\nimport type { CustomRoutes } from '../../lib/load-custom-routes'\nimport {\n  formatIssue,\n  getIssueKey,\n  isRelevantWarning,\n  processIssues,\n  renderStyledStringToErrorAnsi,\n  type EntryIssuesMap,\n  type TopLevelIssuesMap,\n} from '../../shared/lib/turbopack/utils'\n\nconst onceErrorSet = new Set()\n/**\n * Check if given issue is a warning to be display only once.\n * This mimics behavior of get-page-static-info's warnOnce.\n * @param issue\n * @returns\n */\nfunction shouldEmitOnceWarning(issue: Issue): boolean {\n  const { severity, title, stage } = issue\n  if (severity === 'warning' && title.value === 'Invalid page configuration') {\n    if (onceErrorSet.has(issue)) {\n      return false\n    }\n    onceErrorSet.add(issue)\n  }\n  if (\n    severity === 'warning' &&\n    stage === 'config' &&\n    renderStyledStringToErrorAnsi(issue.title).includes(\"can't be external\")\n  ) {\n    if (onceErrorSet.has(issue)) {\n      return false\n    }\n    onceErrorSet.add(issue)\n  }\n\n  return true\n}\n\n/// Print out an issue to the console which should not block\n/// the build by throwing out or blocking error overlay.\nexport function printNonFatalIssue(issue: Issue) {\n  if (isRelevantWarning(issue) && shouldEmitOnceWarning(issue)) {\n    Log.warn(formatIssue(issue))\n  }\n}\n\nexport function processTopLevelIssues(\n  currentTopLevelIssues: TopLevelIssuesMap,\n  result: TurbopackResult\n) {\n  currentTopLevelIssues.clear()\n\n  for (const issue of result.issues) {\n    const issueKey = getIssueKey(issue)\n    currentTopLevelIssues.set(issueKey, issue)\n  }\n}\n\nconst MILLISECONDS_IN_NANOSECOND = BigInt(1_000_000)\n\nexport function msToNs(ms: number): bigint {\n  return BigInt(Math.floor(ms)) * MILLISECONDS_IN_NANOSECOND\n}\n\nexport type ChangeSubscriptions = Map<\n  EntryKey,\n  Promise<AsyncIterableIterator<TurbopackResult>>\n>\n\nexport type HandleWrittenEndpoint = (\n  key: EntryKey,\n  result: TurbopackResult<WrittenEndpoint>,\n  forceDeleteCache: boolean\n) => boolean\n\nexport type StartChangeSubscription = (\n  key: EntryKey,\n  includeIssues: boolean,\n  endpoint: Endpoint,\n  makePayload: (\n    change: TurbopackResult,\n    hash: string\n  ) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void,\n  onError?: (e: Error) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void\n) => Promise<void>\n\nexport type StopChangeSubscription = (key: EntryKey) => Promise<void>\n\nexport type SendHmr = (id: string, payload: HMR_ACTION_TYPES) => void\n\nexport type StartBuilding = (\n  id: string,\n  requestUrl: string | undefined,\n  forceRebuild: boolean\n) => () => void\n\nexport type ReadyIds = Set<string>\n\nexport type ClientState = {\n  clientIssues: EntryIssuesMap\n  hmrPayloads: Map<string, HMR_ACTION_TYPES>\n  turbopackUpdates: TurbopackUpdate[]\n  subscriptions: Map<string, AsyncIterator<any>>\n}\n\nexport type ClientStateMap = WeakMap<ws, ClientState>\n\n// hooks only used by the dev server.\ntype HandleRouteTypeHooks = {\n  handleWrittenEndpoint: HandleWrittenEndpoint\n  subscribeToChanges: StartChangeSubscription\n}\n\nexport async function handleRouteType({\n  dev,\n  page,\n  pathname,\n  route,\n  currentEntryIssues,\n  entrypoints,\n  manifestLoader,\n  readyIds,\n  devRewrites,\n  productionRewrites,\n  hooks,\n  logErrors,\n}: {\n  dev: boolean\n  page: string\n  pathname: string\n  route: PageRoute | AppRoute\n\n  currentEntryIssues: EntryIssuesMap\n  entrypoints: Entrypoints\n  manifestLoader: TurbopackManifestLoader\n  devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n\n  readyIds?: ReadyIds // dev\n\n  hooks?: HandleRouteTypeHooks // dev\n}) {\n  const shouldCreateWebpackStats = process.env.TURBOPACK_STATS != null\n\n  switch (route.type) {\n    case 'page': {\n      const clientKey = getEntryKey('pages', 'client', page)\n      const serverKey = getEntryKey('pages', 'server', page)\n\n      try {\n        // In the best case scenario, Turbopack chunks document, app, page separately in that order,\n        // so it can happen that the chunks of document change, but the chunks of app and page\n        // don't. We still need to reload the page chunks in that case though, otherwise the version\n        // of the document or app component export from the pages template is stale.\n        let documentOrAppChanged = false\n        if (entrypoints.global.app) {\n          const key = getEntryKey('pages', 'server', '_app')\n\n          const writtenEndpoint = await entrypoints.global.app.writeToDisk()\n          documentOrAppChanged ||=\n            hooks?.handleWrittenEndpoint(key, writtenEndpoint, false) ?? false\n          processIssues(\n            currentEntryIssues,\n            key,\n            writtenEndpoint,\n            false,\n            logErrors\n          )\n        }\n        await manifestLoader.loadBuildManifest('_app')\n        await manifestLoader.loadPagesManifest('_app')\n\n        if (entrypoints.global.document) {\n          const key = getEntryKey('pages', 'server', '_document')\n\n          const writtenEndpoint =\n            await entrypoints.global.document.writeToDisk()\n          documentOrAppChanged ||=\n            hooks?.handleWrittenEndpoint(key, writtenEndpoint, false) ?? false\n          processIssues(\n            currentEntryIssues,\n            key,\n            writtenEndpoint,\n            false,\n            logErrors\n          )\n        }\n        await manifestLoader.loadPagesManifest('_document')\n\n        const writtenEndpoint = await route.htmlEndpoint.writeToDisk()\n        hooks?.handleWrittenEndpoint(\n          serverKey,\n          writtenEndpoint,\n          documentOrAppChanged\n        )\n\n        const type = writtenEndpoint?.type\n\n        await manifestLoader.loadBuildManifest(page)\n        await manifestLoader.loadPagesManifest(page)\n        if (type === 'edge') {\n          await manifestLoader.loadMiddlewareManifest(page, 'pages')\n        } else {\n          manifestLoader.deleteMiddlewareManifest(serverKey)\n        }\n        await manifestLoader.loadFontManifest('/_app', 'pages')\n        await manifestLoader.loadFontManifest(page, 'pages')\n\n        if (shouldCreateWebpackStats) {\n          await manifestLoader.loadWebpackStats(page, 'pages')\n        }\n\n        await manifestLoader.writeManifests({\n          devRewrites,\n          productionRewrites,\n          entrypoints,\n        })\n\n        processIssues(\n          currentEntryIssues,\n          serverKey,\n          writtenEndpoint,\n          false,\n          logErrors\n        )\n      } finally {\n        if (dev) {\n          // TODO subscriptions should only be caused by the WebSocket connections\n          // otherwise we don't known when to unsubscribe and this leaking\n          hooks?.subscribeToChanges(\n            serverKey,\n            false,\n            route.dataEndpoint,\n            () => {\n              // Report the next compilation again\n              readyIds?.delete(pathname)\n              return {\n                event: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES,\n                pages: [page],\n              }\n            },\n            (e) => {\n              return {\n                action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                data: `error in ${page} data subscription: ${e}`,\n              }\n            }\n          )\n          hooks?.subscribeToChanges(\n            clientKey,\n            false,\n            route.htmlEndpoint,\n            () => {\n              return {\n                event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES,\n              }\n            },\n            (e) => {\n              return {\n                action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                data: `error in ${page} html subscription: ${e}`,\n              }\n            }\n          )\n          if (entrypoints.global.document) {\n            hooks?.subscribeToChanges(\n              getEntryKey('pages', 'server', '_document'),\n              false,\n              entrypoints.global.document,\n              () => {\n                return {\n                  action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                  data: '_document has changed (page route)',\n                }\n              },\n              (e) => {\n                return {\n                  action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                  data: `error in _document subscription (page route): ${e}`,\n                }\n              }\n            )\n          }\n        }\n      }\n\n      break\n    }\n    case 'page-api': {\n      const key = getEntryKey('pages', 'server', page)\n\n      const writtenEndpoint = await route.endpoint.writeToDisk()\n      hooks?.handleWrittenEndpoint(key, writtenEndpoint, false)\n\n      const type = writtenEndpoint.type\n\n      await manifestLoader.loadPagesManifest(page)\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(currentEntryIssues, key, writtenEndpoint, true, logErrors)\n\n      break\n    }\n    case 'app-page': {\n      const key = getEntryKey('app', 'server', page)\n\n      const writtenEndpoint = await route.htmlEndpoint.writeToDisk()\n      hooks?.handleWrittenEndpoint(key, writtenEndpoint, false)\n\n      if (dev) {\n        // TODO subscriptions should only be caused by the WebSocket connections\n        // otherwise we don't known when to unsubscribe and this leaking\n        hooks?.subscribeToChanges(\n          key,\n          true,\n          route.rscEndpoint,\n          (change, hash) => {\n            if (change.issues.some((issue) => issue.severity === 'error')) {\n              // Ignore any updates that has errors\n              // There will be another update without errors eventually\n              return\n            }\n            // Report the next compilation again\n            readyIds?.delete(pathname)\n            return {\n              action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES,\n              hash,\n            }\n          },\n          (e) => {\n            return {\n              action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n              data: `error in ${page} app-page subscription: ${e}`,\n            }\n          }\n        )\n      }\n\n      const type = writtenEndpoint.type\n\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.loadAppBuildManifest(page)\n      await manifestLoader.loadBuildManifest(page, 'app')\n      await manifestLoader.loadAppPathsManifest(page)\n      await manifestLoader.loadActionManifest(page)\n      await manifestLoader.loadFontManifest(page, 'app')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'app')\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(currentEntryIssues, key, writtenEndpoint, dev, logErrors)\n\n      break\n    }\n    case 'app-route': {\n      const key = getEntryKey('app', 'server', page)\n\n      const writtenEndpoint = await route.endpoint.writeToDisk()\n      hooks?.handleWrittenEndpoint(key, writtenEndpoint, false)\n\n      const type = writtenEndpoint.type\n\n      await manifestLoader.loadAppPathsManifest(page)\n\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites,\n        productionRewrites,\n        entrypoints,\n      })\n      processIssues(currentEntryIssues, key, writtenEndpoint, true, logErrors)\n\n      break\n    }\n    default: {\n      throw new Error(`unknown route type ${(route as any).type} for ${page}`)\n    }\n  }\n}\n\n/**\n * Maintains a mapping between entrypoins and the corresponding client asset paths.\n */\nexport class AssetMapper {\n  private entryMap: Map<EntryKey, Set<string>> = new Map()\n  private assetMap: Map<string, Set<EntryKey>> = new Map()\n\n  /**\n   * Overrides asset paths for a key and updates the mapping from path to key.\n   *\n   * @param key\n   * @param assetPaths asset paths relative to the .next directory\n   */\n  setPathsForKey(key: EntryKey, assetPaths: string[]): void {\n    this.delete(key)\n\n    const newAssetPaths = new Set(assetPaths)\n    this.entryMap.set(key, newAssetPaths)\n\n    for (const assetPath of newAssetPaths) {\n      let assetPathKeys = this.assetMap.get(assetPath)\n      if (!assetPathKeys) {\n        assetPathKeys = new Set()\n        this.assetMap.set(assetPath, assetPathKeys)\n      }\n\n      assetPathKeys!.add(key)\n    }\n  }\n\n  /**\n   * Deletes the key and any asset only referenced by this key.\n   *\n   * @param key\n   */\n  delete(key: EntryKey) {\n    for (const assetPath of this.getAssetPathsByKey(key)) {\n      const assetPathKeys = this.assetMap.get(assetPath)\n\n      assetPathKeys?.delete(key)\n\n      if (!assetPathKeys?.size) {\n        this.assetMap.delete(assetPath)\n      }\n    }\n\n    this.entryMap.delete(key)\n  }\n\n  getAssetPathsByKey(key: EntryKey): string[] {\n    return Array.from(this.entryMap.get(key) ?? [])\n  }\n\n  getKeysByAsset(path: string): EntryKey[] {\n    return Array.from(this.assetMap.get(path) ?? [])\n  }\n\n  keys(): IterableIterator<EntryKey> {\n    return this.entryMap.keys()\n  }\n}\n\nexport function hasEntrypointForKey(\n  entrypoints: Entrypoints,\n  key: EntryKey,\n  assetMapper: AssetMapper | undefined\n): boolean {\n  const { type, page } = splitEntryKey(key)\n\n  switch (type) {\n    case 'app':\n      return entrypoints.app.has(page)\n    case 'pages':\n      switch (page) {\n        case '_app':\n          return entrypoints.global.app != null\n        case '_document':\n          return entrypoints.global.document != null\n        case '_error':\n          return entrypoints.global.error != null\n        default:\n          return entrypoints.page.has(page)\n      }\n    case 'root':\n      switch (page) {\n        case 'middleware':\n          return entrypoints.global.middleware != null\n        case 'instrumentation':\n          return entrypoints.global.instrumentation != null\n        default:\n          return false\n      }\n    case 'assets':\n      if (!assetMapper) {\n        return false\n      }\n\n      return assetMapper\n        .getKeysByAsset(page)\n        .some((pageKey) =>\n          hasEntrypointForKey(entrypoints, pageKey, assetMapper)\n        )\n    default: {\n      // validation that we covered all cases, this should never run.\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const _: never = type\n      return false\n    }\n  }\n}\n\n// hooks only used by the dev server.\ntype HandleEntrypointsHooks = {\n  handleWrittenEndpoint: HandleWrittenEndpoint\n  propagateServerField: (\n    field: PropagateToWorkersField,\n    args: any\n  ) => Promise<void>\n  sendHmr: SendHmr\n  startBuilding: StartBuilding\n  subscribeToChanges: StartChangeSubscription\n  unsubscribeFromChanges: StopChangeSubscription\n  unsubscribeFromHmrEvents: (client: ws, id: string) => void\n}\n\ntype HandleEntrypointsDevOpts = {\n  assetMapper: AssetMapper\n  changeSubscriptions: ChangeSubscriptions\n  clients: Set<ws>\n  clientStates: ClientStateMap\n  serverFields: ServerFields\n\n  hooks: HandleEntrypointsHooks\n}\n\nexport async function handleEntrypoints({\n  entrypoints,\n\n  currentEntrypoints,\n\n  currentEntryIssues,\n  manifestLoader,\n  devRewrites,\n  logErrors,\n  dev,\n}: {\n  entrypoints: TurbopackResult<RawEntrypoints>\n\n  currentEntrypoints: Entrypoints\n\n  currentEntryIssues: EntryIssuesMap\n  manifestLoader: TurbopackManifestLoader\n  devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n\n  dev: HandleEntrypointsDevOpts\n}) {\n  currentEntrypoints.global.app = entrypoints.pagesAppEndpoint\n  currentEntrypoints.global.document = entrypoints.pagesDocumentEndpoint\n  currentEntrypoints.global.error = entrypoints.pagesErrorEndpoint\n\n  currentEntrypoints.global.instrumentation = entrypoints.instrumentation\n\n  currentEntrypoints.page.clear()\n  currentEntrypoints.app.clear()\n\n  for (const [pathname, route] of entrypoints.routes) {\n    switch (route.type) {\n      case 'page':\n      case 'page-api':\n        currentEntrypoints.page.set(pathname, route)\n        break\n      case 'app-page': {\n        route.pages.forEach((page) => {\n          currentEntrypoints.app.set(page.originalName, {\n            type: 'app-page',\n            ...page,\n          })\n        })\n        break\n      }\n      case 'app-route': {\n        currentEntrypoints.app.set(route.originalName, route)\n        break\n      }\n      default:\n        Log.info(`skipping ${pathname} (${route.type})`)\n        break\n    }\n  }\n\n  if (dev) {\n    await handleEntrypointsDevCleanup({\n      currentEntryIssues,\n      currentEntrypoints,\n\n      ...dev,\n    })\n  }\n\n  const { middleware, instrumentation } = entrypoints\n\n  // We check for explicit true/false, since it's initialized to\n  // undefined during the first loop (middlewareChanges event is\n  // unnecessary during the first serve)\n  if (currentEntrypoints.global.middleware && !middleware) {\n    const key = getEntryKey('root', 'server', 'middleware')\n    // Went from middleware to no middleware\n    await dev?.hooks.unsubscribeFromChanges(key)\n    currentEntryIssues.delete(key)\n    dev.hooks.sendHmr('middleware', {\n      event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n    })\n  } else if (!currentEntrypoints.global.middleware && middleware) {\n    // Went from no middleware to middleware\n    dev.hooks.sendHmr('middleware', {\n      event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n    })\n  }\n\n  currentEntrypoints.global.middleware = middleware\n\n  if (instrumentation) {\n    const processInstrumentation = async (\n      name: string,\n      prop: 'nodeJs' | 'edge'\n    ) => {\n      const prettyName = {\n        nodeJs: 'Node.js',\n        edge: 'Edge',\n      }\n      const finishBuilding = dev.hooks.startBuilding(\n        `instrumentation ${prettyName[prop]}`,\n        undefined,\n        true\n      )\n      const key = getEntryKey('root', 'server', name)\n\n      const writtenEndpoint = await instrumentation[prop].writeToDisk()\n      dev.hooks.handleWrittenEndpoint(key, writtenEndpoint, false)\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n      finishBuilding()\n    }\n    await processInstrumentation('instrumentation.nodeJs', 'nodeJs')\n    await processInstrumentation('instrumentation.edge', 'edge')\n    await manifestLoader.loadMiddlewareManifest(\n      'instrumentation',\n      'instrumentation'\n    )\n    await manifestLoader.writeManifests({\n      devRewrites,\n      productionRewrites: undefined,\n      entrypoints: currentEntrypoints,\n    })\n\n    dev.serverFields.actualInstrumentationHookFile = '/instrumentation'\n    await dev.hooks.propagateServerField(\n      'actualInstrumentationHookFile',\n      dev.serverFields.actualInstrumentationHookFile\n    )\n  } else {\n    dev.serverFields.actualInstrumentationHookFile = undefined\n    await dev.hooks.propagateServerField(\n      'actualInstrumentationHookFile',\n      dev.serverFields.actualInstrumentationHookFile\n    )\n  }\n\n  if (middleware) {\n    const key = getEntryKey('root', 'server', 'middleware')\n\n    const endpoint = middleware.endpoint\n\n    async function processMiddleware() {\n      const finishBuilding = dev.hooks.startBuilding(\n        'middleware',\n        undefined,\n        true\n      )\n      const writtenEndpoint = await endpoint.writeToDisk()\n      dev.hooks.handleWrittenEndpoint(key, writtenEndpoint, false)\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n      await manifestLoader.loadMiddlewareManifest('middleware', 'middleware')\n      const middlewareConfig =\n        manifestLoader.getMiddlewareManifest(key)?.middleware['/']\n\n      if (dev && middlewareConfig) {\n        dev.serverFields.middleware = {\n          match: null as any,\n          page: '/',\n          matchers: middlewareConfig.matchers,\n        }\n      }\n      finishBuilding()\n    }\n    await processMiddleware()\n\n    if (dev) {\n      dev?.hooks.subscribeToChanges(\n        key,\n        false,\n        endpoint,\n        async () => {\n          const finishBuilding = dev.hooks.startBuilding(\n            'middleware',\n            undefined,\n            true\n          )\n          await processMiddleware()\n          await dev.hooks.propagateServerField(\n            'actualMiddlewareFile',\n            dev.serverFields.actualMiddlewareFile\n          )\n          await dev.hooks.propagateServerField(\n            'middleware',\n            dev.serverFields.middleware\n          )\n          await manifestLoader.writeManifests({\n            devRewrites,\n            productionRewrites: undefined,\n            entrypoints: currentEntrypoints,\n          })\n\n          finishBuilding?.()\n          return { event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES }\n        },\n        () => {\n          return {\n            event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n          }\n        }\n      )\n    }\n  } else {\n    manifestLoader.deleteMiddlewareManifest(\n      getEntryKey('root', 'server', 'middleware')\n    )\n    dev.serverFields.actualMiddlewareFile = undefined\n    dev.serverFields.middleware = undefined\n  }\n\n  await dev.hooks.propagateServerField(\n    'actualMiddlewareFile',\n    dev.serverFields.actualMiddlewareFile\n  )\n  await dev.hooks.propagateServerField(\n    'middleware',\n    dev.serverFields.middleware\n  )\n}\n\nasync function handleEntrypointsDevCleanup({\n  currentEntryIssues,\n  currentEntrypoints,\n\n  assetMapper,\n  changeSubscriptions,\n  clients,\n  clientStates,\n\n  hooks,\n}: {\n  currentEntrypoints: Entrypoints\n  currentEntryIssues: EntryIssuesMap\n} & HandleEntrypointsDevOpts) {\n  // this needs to be first as `hasEntrypointForKey` uses the `assetMapper`\n  for (const key of assetMapper.keys()) {\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      assetMapper.delete(key)\n    }\n  }\n\n  for (const key of changeSubscriptions.keys()) {\n    // middleware is handled separately\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      await hooks.unsubscribeFromChanges(key)\n    }\n  }\n\n  for (const [key] of currentEntryIssues) {\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      currentEntryIssues.delete(key)\n    }\n  }\n\n  for (const client of clients) {\n    const state = clientStates.get(client)\n    if (!state) {\n      continue\n    }\n\n    for (const key of state.clientIssues.keys()) {\n      if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n        state.clientIssues.delete(key)\n      }\n    }\n\n    for (const id of state.subscriptions.keys()) {\n      if (\n        !hasEntrypointForKey(\n          currentEntrypoints,\n          getEntryKey('assets', 'client', id),\n          assetMapper\n        )\n      ) {\n        hooks.unsubscribeFromHmrEvents(client, id)\n      }\n    }\n  }\n}\n\nexport async function handlePagesErrorRoute({\n  currentEntryIssues,\n  entrypoints,\n  manifestLoader,\n  devRewrites,\n  productionRewrites,\n  logErrors,\n  hooks,\n}: {\n  currentEntryIssues: EntryIssuesMap\n  entrypoints: Entrypoints\n  manifestLoader: TurbopackManifestLoader\n  devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n  hooks: HandleRouteTypeHooks\n}) {\n  if (entrypoints.global.app) {\n    const key = getEntryKey('pages', 'server', '_app')\n\n    const writtenEndpoint = await entrypoints.global.app.writeToDisk()\n    hooks.handleWrittenEndpoint(key, writtenEndpoint, false)\n    hooks.subscribeToChanges(\n      key,\n      false,\n      entrypoints.global.app,\n      () => {\n        // There's a special case for this in `../client/page-bootstrap.ts`.\n        // https://github.com/vercel/next.js/blob/08d7a7e5189a835f5dcb82af026174e587575c0e/packages/next/src/client/page-bootstrap.ts#L69-L71\n        return { event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES }\n      },\n      () => {\n        return {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: '_app has changed (error route)',\n        }\n      }\n    )\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadBuildManifest('_app')\n  await manifestLoader.loadPagesManifest('_app')\n  await manifestLoader.loadFontManifest('_app')\n\n  if (entrypoints.global.document) {\n    const key = getEntryKey('pages', 'server', '_document')\n\n    const writtenEndpoint = await entrypoints.global.document.writeToDisk()\n    hooks.handleWrittenEndpoint(key, writtenEndpoint, false)\n    hooks.subscribeToChanges(\n      key,\n      false,\n      entrypoints.global.document,\n      () => {\n        return {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: '_document has changed (error route)',\n        }\n      },\n      (e) => {\n        return {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: `error in _document subscription (error route): ${e}`,\n        }\n      }\n    )\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadPagesManifest('_document')\n\n  if (entrypoints.global.error) {\n    const key = getEntryKey('pages', 'server', '_error')\n\n    const writtenEndpoint = await entrypoints.global.error.writeToDisk()\n    hooks.handleWrittenEndpoint(key, writtenEndpoint, false)\n    hooks.subscribeToChanges(\n      key,\n      false,\n      entrypoints.global.error,\n      () => {\n        // There's a special case for this in `../client/page-bootstrap.ts`.\n        // https://github.com/vercel/next.js/blob/08d7a7e5189a835f5dcb82af026174e587575c0e/packages/next/src/client/page-bootstrap.ts#L69-L71\n        return { event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES }\n      },\n      (e) => {\n        return {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: `error in _error subscription: ${e}`,\n        }\n      }\n    )\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadBuildManifest('_error')\n  await manifestLoader.loadPagesManifest('_error')\n  await manifestLoader.loadFontManifest('_error')\n\n  await manifestLoader.writeManifests({\n    devRewrites,\n    productionRewrites,\n    entrypoints,\n  })\n}\n\nexport function removeRouteSuffix(route: string): string {\n  return route.replace(/\\/route$/, '')\n}\n\nexport function addRouteSuffix(route: string): string {\n  return route + '/route'\n}\n\nexport function addMetadataIdToRoute(route: string): string {\n  return route + '/[__metadata_id__]'\n}\n\n// Since turbopack will create app pages/route entries based on the structure,\n// which means the entry keys are based on file names.\n// But for special metadata conventions we'll change the page/pathname to a different path.\n// So we need this helper to map the new path back to original turbopack entry key.\nexport function normalizedPageToTurbopackStructureRoute(\n  route: string,\n  ext: string | false\n): string {\n  let entrypointKey = route\n  if (isMetadataRoute(entrypointKey)) {\n    entrypointKey = entrypointKey.endsWith('/route')\n      ? entrypointKey.slice(0, -'/route'.length)\n      : entrypointKey\n\n    if (ext) {\n      if (entrypointKey.endsWith('/[__metadata_id__]')) {\n        entrypointKey = entrypointKey.slice(0, -'/[__metadata_id__]'.length)\n      }\n      if (entrypointKey.endsWith('/sitemap.xml') && ext !== '.xml') {\n        // For dynamic sitemap route, remove the extension\n        entrypointKey = entrypointKey.slice(0, -'.xml'.length)\n      }\n    }\n    entrypointKey = entrypointKey + '/route'\n  }\n  return entrypointKey\n}\n"], "names": ["AssetMapper", "addMetadataIdToRoute", "addRouteSuffix", "handleEntrypoints", "handlePagesErrorRoute", "handleRouteType", "hasEntrypointForKey", "msToNs", "normalizedPageToTurbopackStructureRoute", "printNonFatalIssue", "processTopLevelIssues", "removeRouteSuffix", "onceErrorSet", "Set", "shouldEmitOnceWarning", "issue", "severity", "title", "stage", "value", "has", "add", "renderStyledStringToErrorAnsi", "includes", "isRelevantWarning", "Log", "warn", "formatIssue", "currentTopLevelIssues", "result", "clear", "issues", "issue<PERSON><PERSON>", "getIssueKey", "set", "MILLISECONDS_IN_NANOSECOND", "BigInt", "ms", "Math", "floor", "dev", "page", "pathname", "route", "currentEntryIssues", "entrypoints", "manifest<PERSON><PERSON>der", "readyIds", "devRewrites", "productionRewrites", "hooks", "logErrors", "shouldCreateWebpackStats", "process", "env", "TURBOPACK_STATS", "type", "client<PERSON>ey", "getEntry<PERSON>ey", "server<PERSON>ey", "documentOrAppChanged", "global", "app", "key", "writtenEndpoint", "writeToDisk", "handleWrittenEndpoint", "processIssues", "loadBuildManifest", "loadPagesManifest", "document", "htmlEndpoint", "loadMiddlewareManifest", "deleteMiddlewareManifest", "loadFontManifest", "loadWebpackStats", "writeManifests", "subscribeToChanges", "dataEndpoint", "delete", "event", "HMR_ACTIONS_SENT_TO_BROWSER", "SERVER_ONLY_CHANGES", "pages", "e", "action", "RELOAD_PAGE", "data", "CLIENT_CHANGES", "endpoint", "rscEndpoint", "change", "hash", "some", "SERVER_COMPONENT_CHANGES", "loadAppBuildManifest", "loadAppPathsManifest", "loadActionManifest", "Error", "setPathsFor<PERSON>ey", "assetPaths", "newAssetPaths", "entryMap", "assetPath", "assetPathKeys", "assetMap", "get", "getAssetPathsByKey", "size", "Array", "from", "getKeysByAsset", "path", "keys", "Map", "assetMapper", "splitEntryKey", "error", "middleware", "instrumentation", "page<PERSON><PERSON>", "_", "currentEntrypoints", "pagesAppEndpoint", "pagesDocumentEndpoint", "pagesErrorEndpoint", "routes", "for<PERSON>ach", "originalName", "info", "handleEntrypointsDevCleanup", "unsubscribeFromChanges", "sendHmr", "MIDDLEWARE_CHANGES", "processInstrumentation", "name", "prop", "<PERSON><PERSON><PERSON>", "nodeJs", "edge", "finishBuilding", "startBuilding", "undefined", "serverFields", "actualInstrumentationHookFile", "propagateServerField", "processMiddleware", "middlewareConfig", "getMiddlewareManifest", "match", "matchers", "actualMiddlewareFile", "changeSubscriptions", "clients", "clientStates", "client", "state", "clientIssues", "id", "subscriptions", "unsubscribeFromHmrEvents", "replace", "ext", "entrypoint<PERSON><PERSON>", "isMetadataRoute", "endsWith", "slice", "length"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IAwbaA,WAAW;eAAXA;;IAygBGC,oBAAoB;eAApBA;;IAJAC,cAAc;eAAdA;;IAjYMC,iBAAiB;eAAjBA;;IAqRAC,qBAAqB;eAArBA;;IAnsBAC,eAAe;eAAfA;;IAqWNC,mBAAmB;eAAnBA;;IA1ZAC,MAAM;eAANA;;IAg3BAC,uCAAuC;eAAvCA;;IAp4BAC,kBAAkB;eAAlBA;;IAMAC,qBAAqB;eAArBA;;IA82BAC,iBAAiB;eAAjBA;;;kCA16BT;6DACc;0BAQd;iCAEyB;uBAUzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,MAAMC,eAAe,IAAIC;AACzB;;;;;CAKC,GACD,SAASC,sBAAsBC,KAAY;IACzC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,EAAE,GAAGH;IACnC,IAAIC,aAAa,aAAaC,MAAME,KAAK,KAAK,8BAA8B;QAC1E,IAAIP,aAAaQ,GAAG,CAACL,QAAQ;YAC3B,OAAO;QACT;QACAH,aAAaS,GAAG,CAACN;IACnB;IACA,IACEC,aAAa,aACbE,UAAU,YACVI,IAAAA,oCAA6B,EAACP,MAAME,KAAK,EAAEM,QAAQ,CAAC,sBACpD;QACA,IAAIX,aAAaQ,GAAG,CAACL,QAAQ;YAC3B,OAAO;QACT;QACAH,aAAaS,GAAG,CAACN;IACnB;IAEA,OAAO;AACT;AAIO,SAASN,mBAAmBM,KAAY;IAC7C,IAAIS,IAAAA,wBAAiB,EAACT,UAAUD,sBAAsBC,QAAQ;QAC5DU,KAAIC,IAAI,CAACC,IAAAA,kBAAW,EAACZ;IACvB;AACF;AAEO,SAASL,sBACdkB,qBAAwC,EACxCC,MAAuB;IAEvBD,sBAAsBE,KAAK;IAE3B,KAAK,MAAMf,SAASc,OAAOE,MAAM,CAAE;QACjC,MAAMC,WAAWC,IAAAA,kBAAW,EAAClB;QAC7Ba,sBAAsBM,GAAG,CAACF,UAAUjB;IACtC;AACF;AAEA,MAAMoB,6BAA6BC,OAAO;AAEnC,SAAS7B,OAAO8B,EAAU;IAC/B,OAAOD,OAAOE,KAAKC,KAAK,CAACF,OAAOF;AAClC;AAmDO,eAAe9B,gBAAgB,EACpCmC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,kBAAkB,EAClBC,WAAW,EACXC,cAAc,EACdC,QAAQ,EACRC,WAAW,EACXC,kBAAkB,EAClBC,KAAK,EACLC,SAAS,EAiBV;IACC,MAAMC,2BAA2BC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAEhE,OAAQZ,MAAMa,IAAI;QAChB,KAAK;YAAQ;gBACX,MAAMC,YAAYC,IAAAA,qBAAW,EAAC,SAAS,UAAUjB;gBACjD,MAAMkB,YAAYD,IAAAA,qBAAW,EAAC,SAAS,UAAUjB;gBAEjD,IAAI;oBACF,4FAA4F;oBAC5F,sFAAsF;oBACtF,4FAA4F;oBAC5F,4EAA4E;oBAC5E,IAAImB,uBAAuB;oBAC3B,IAAIf,YAAYgB,MAAM,CAACC,GAAG,EAAE;wBAC1B,MAAMC,MAAML,IAAAA,qBAAW,EAAC,SAAS,UAAU;wBAE3C,MAAMM,kBAAkB,MAAMnB,YAAYgB,MAAM,CAACC,GAAG,CAACG,WAAW;wBAChEL,yBACEV,CAAAA,yBAAAA,MAAOgB,qBAAqB,CAACH,KAAKC,iBAAiB,WAAU;wBAC/DG,IAAAA,oBAAa,EACXvB,oBACAmB,KACAC,iBACA,OACAb;oBAEJ;oBACA,MAAML,eAAesB,iBAAiB,CAAC;oBACvC,MAAMtB,eAAeuB,iBAAiB,CAAC;oBAEvC,IAAIxB,YAAYgB,MAAM,CAACS,QAAQ,EAAE;wBAC/B,MAAMP,MAAML,IAAAA,qBAAW,EAAC,SAAS,UAAU;wBAE3C,MAAMM,kBACJ,MAAMnB,YAAYgB,MAAM,CAACS,QAAQ,CAACL,WAAW;wBAC/CL,yBACEV,CAAAA,yBAAAA,MAAOgB,qBAAqB,CAACH,KAAKC,iBAAiB,WAAU;wBAC/DG,IAAAA,oBAAa,EACXvB,oBACAmB,KACAC,iBACA,OACAb;oBAEJ;oBACA,MAAML,eAAeuB,iBAAiB,CAAC;oBAEvC,MAAML,kBAAkB,MAAMrB,MAAM4B,YAAY,CAACN,WAAW;oBAC5Df,yBAAAA,MAAOgB,qBAAqB,CAC1BP,WACAK,iBACAJ;oBAGF,MAAMJ,OAAOQ,mCAAAA,gBAAiBR,IAAI;oBAElC,MAAMV,eAAesB,iBAAiB,CAAC3B;oBACvC,MAAMK,eAAeuB,iBAAiB,CAAC5B;oBACvC,IAAIe,SAAS,QAAQ;wBACnB,MAAMV,eAAe0B,sBAAsB,CAAC/B,MAAM;oBACpD,OAAO;wBACLK,eAAe2B,wBAAwB,CAACd;oBAC1C;oBACA,MAAMb,eAAe4B,gBAAgB,CAAC,SAAS;oBAC/C,MAAM5B,eAAe4B,gBAAgB,CAACjC,MAAM;oBAE5C,IAAIW,0BAA0B;wBAC5B,MAAMN,eAAe6B,gBAAgB,CAAClC,MAAM;oBAC9C;oBAEA,MAAMK,eAAe8B,cAAc,CAAC;wBAClC5B;wBACAC;wBACAJ;oBACF;oBAEAsB,IAAAA,oBAAa,EACXvB,oBACAe,WACAK,iBACA,OACAb;gBAEJ,SAAU;oBACR,IAAIX,KAAK;wBACP,wEAAwE;wBACxE,gEAAgE;wBAChEU,yBAAAA,MAAO2B,kBAAkB,CACvBlB,WACA,OACAhB,MAAMmC,YAAY,EAClB;4BACE,oCAAoC;4BACpC/B,4BAAAA,SAAUgC,MAAM,CAACrC;4BACjB,OAAO;gCACLsC,OAAOC,6CAA2B,CAACC,mBAAmB;gCACtDC,OAAO;oCAAC1C;iCAAK;4BACf;wBACF,GACA,CAAC2C;4BACC,OAAO;gCACLC,QAAQJ,6CAA2B,CAACK,WAAW;gCAC/CC,MAAM,CAAC,SAAS,EAAE9C,KAAK,oBAAoB,EAAE2C,GAAG;4BAClD;wBACF;wBAEFlC,yBAAAA,MAAO2B,kBAAkB,CACvBpB,WACA,OACAd,MAAM4B,YAAY,EAClB;4BACE,OAAO;gCACLS,OAAOC,6CAA2B,CAACO,cAAc;4BACnD;wBACF,GACA,CAACJ;4BACC,OAAO;gCACLC,QAAQJ,6CAA2B,CAACK,WAAW;gCAC/CC,MAAM,CAAC,SAAS,EAAE9C,KAAK,oBAAoB,EAAE2C,GAAG;4BAClD;wBACF;wBAEF,IAAIvC,YAAYgB,MAAM,CAACS,QAAQ,EAAE;4BAC/BpB,yBAAAA,MAAO2B,kBAAkB,CACvBnB,IAAAA,qBAAW,EAAC,SAAS,UAAU,cAC/B,OACAb,YAAYgB,MAAM,CAACS,QAAQ,EAC3B;gCACE,OAAO;oCACLe,QAAQJ,6CAA2B,CAACK,WAAW;oCAC/CC,MAAM;gCACR;4BACF,GACA,CAACH;gCACC,OAAO;oCACLC,QAAQJ,6CAA2B,CAACK,WAAW;oCAC/CC,MAAM,CAAC,8CAA8C,EAAEH,GAAG;gCAC5D;4BACF;wBAEJ;oBACF;gBACF;gBAEA;YACF;QACA,KAAK;YAAY;gBACf,MAAMrB,MAAML,IAAAA,qBAAW,EAAC,SAAS,UAAUjB;gBAE3C,MAAMuB,kBAAkB,MAAMrB,MAAM8C,QAAQ,CAACxB,WAAW;gBACxDf,yBAAAA,MAAOgB,qBAAqB,CAACH,KAAKC,iBAAiB;gBAEnD,MAAMR,OAAOQ,gBAAgBR,IAAI;gBAEjC,MAAMV,eAAeuB,iBAAiB,CAAC5B;gBACvC,IAAIe,SAAS,QAAQ;oBACnB,MAAMV,eAAe0B,sBAAsB,CAAC/B,MAAM;gBACpD,OAAO;oBACLK,eAAe2B,wBAAwB,CAACV;gBAC1C;gBAEA,MAAMjB,eAAe8B,cAAc,CAAC;oBAClC5B;oBACAC;oBACAJ;gBACF;gBAEAsB,IAAAA,oBAAa,EAACvB,oBAAoBmB,KAAKC,iBAAiB,MAAMb;gBAE9D;YACF;QACA,KAAK;YAAY;gBACf,MAAMY,MAAML,IAAAA,qBAAW,EAAC,OAAO,UAAUjB;gBAEzC,MAAMuB,kBAAkB,MAAMrB,MAAM4B,YAAY,CAACN,WAAW;gBAC5Df,yBAAAA,MAAOgB,qBAAqB,CAACH,KAAKC,iBAAiB;gBAEnD,IAAIxB,KAAK;oBACP,wEAAwE;oBACxE,gEAAgE;oBAChEU,yBAAAA,MAAO2B,kBAAkB,CACvBd,KACA,MACApB,MAAM+C,WAAW,EACjB,CAACC,QAAQC;wBACP,IAAID,OAAO5D,MAAM,CAAC8D,IAAI,CAAC,CAAC9E,QAAUA,MAAMC,QAAQ,KAAK,UAAU;4BAC7D,qCAAqC;4BACrC,yDAAyD;4BACzD;wBACF;wBACA,oCAAoC;wBACpC+B,4BAAAA,SAAUgC,MAAM,CAACrC;wBACjB,OAAO;4BACL2C,QAAQJ,6CAA2B,CAACa,wBAAwB;4BAC5DF;wBACF;oBACF,GACA,CAACR;wBACC,OAAO;4BACLC,QAAQJ,6CAA2B,CAACK,WAAW;4BAC/CC,MAAM,CAAC,SAAS,EAAE9C,KAAK,wBAAwB,EAAE2C,GAAG;wBACtD;oBACF;gBAEJ;gBAEA,MAAM5B,OAAOQ,gBAAgBR,IAAI;gBAEjC,IAAIA,SAAS,QAAQ;oBACnB,MAAMV,eAAe0B,sBAAsB,CAAC/B,MAAM;gBACpD,OAAO;oBACLK,eAAe2B,wBAAwB,CAACV;gBAC1C;gBAEA,MAAMjB,eAAeiD,oBAAoB,CAACtD;gBAC1C,MAAMK,eAAesB,iBAAiB,CAAC3B,MAAM;gBAC7C,MAAMK,eAAekD,oBAAoB,CAACvD;gBAC1C,MAAMK,eAAemD,kBAAkB,CAACxD;gBACxC,MAAMK,eAAe4B,gBAAgB,CAACjC,MAAM;gBAE5C,IAAIW,0BAA0B;oBAC5B,MAAMN,eAAe6B,gBAAgB,CAAClC,MAAM;gBAC9C;gBAEA,MAAMK,eAAe8B,cAAc,CAAC;oBAClC5B;oBACAC;oBACAJ;gBACF;gBAEAsB,IAAAA,oBAAa,EAACvB,oBAAoBmB,KAAKC,iBAAiBxB,KAAKW;gBAE7D;YACF;QACA,KAAK;YAAa;gBAChB,MAAMY,MAAML,IAAAA,qBAAW,EAAC,OAAO,UAAUjB;gBAEzC,MAAMuB,kBAAkB,MAAMrB,MAAM8C,QAAQ,CAACxB,WAAW;gBACxDf,yBAAAA,MAAOgB,qBAAqB,CAACH,KAAKC,iBAAiB;gBAEnD,MAAMR,OAAOQ,gBAAgBR,IAAI;gBAEjC,MAAMV,eAAekD,oBAAoB,CAACvD;gBAE1C,IAAIe,SAAS,QAAQ;oBACnB,MAAMV,eAAe0B,sBAAsB,CAAC/B,MAAM;gBACpD,OAAO;oBACLK,eAAe2B,wBAAwB,CAACV;gBAC1C;gBAEA,MAAMjB,eAAe8B,cAAc,CAAC;oBAClC5B;oBACAC;oBACAJ;gBACF;gBACAsB,IAAAA,oBAAa,EAACvB,oBAAoBmB,KAAKC,iBAAiB,MAAMb;gBAE9D;YACF;QACA;YAAS;gBACP,MAAM,qBAAkE,CAAlE,IAAI+C,MAAM,CAAC,mBAAmB,EAAE,AAACvD,MAAca,IAAI,CAAC,KAAK,EAAEf,MAAM,GAAjE,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiE;YACzE;IACF;AACF;AAKO,MAAMzC;IAIX;;;;;GAKC,GACDmG,eAAepC,GAAa,EAAEqC,UAAoB,EAAQ;QACxD,IAAI,CAACrB,MAAM,CAAChB;QAEZ,MAAMsC,gBAAgB,IAAIxF,IAAIuF;QAC9B,IAAI,CAACE,QAAQ,CAACpE,GAAG,CAAC6B,KAAKsC;QAEvB,KAAK,MAAME,aAAaF,cAAe;YACrC,IAAIG,gBAAgB,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACH;YACtC,IAAI,CAACC,eAAe;gBAClBA,gBAAgB,IAAI3F;gBACpB,IAAI,CAAC4F,QAAQ,CAACvE,GAAG,CAACqE,WAAWC;YAC/B;YAEAA,cAAenF,GAAG,CAAC0C;QACrB;IACF;IAEA;;;;GAIC,GACDgB,OAAOhB,GAAa,EAAE;QACpB,KAAK,MAAMwC,aAAa,IAAI,CAACI,kBAAkB,CAAC5C,KAAM;YACpD,MAAMyC,gBAAgB,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACH;YAExCC,iCAAAA,cAAezB,MAAM,CAAChB;YAEtB,IAAI,EAACyC,iCAAAA,cAAeI,IAAI,GAAE;gBACxB,IAAI,CAACH,QAAQ,CAAC1B,MAAM,CAACwB;YACvB;QACF;QAEA,IAAI,CAACD,QAAQ,CAACvB,MAAM,CAAChB;IACvB;IAEA4C,mBAAmB5C,GAAa,EAAY;QAC1C,OAAO8C,MAAMC,IAAI,CAAC,IAAI,CAACR,QAAQ,CAACI,GAAG,CAAC3C,QAAQ,EAAE;IAChD;IAEAgD,eAAeC,IAAY,EAAc;QACvC,OAAOH,MAAMC,IAAI,CAAC,IAAI,CAACL,QAAQ,CAACC,GAAG,CAACM,SAAS,EAAE;IACjD;IAEAC,OAAmC;QACjC,OAAO,IAAI,CAACX,QAAQ,CAACW,IAAI;IAC3B;;aAvDQX,WAAuC,IAAIY;aAC3CT,WAAuC,IAAIS;;AAuDrD;AAEO,SAAS5G,oBACduC,WAAwB,EACxBkB,GAAa,EACboD,WAAoC;IAEpC,MAAM,EAAE3D,IAAI,EAAEf,IAAI,EAAE,GAAG2E,IAAAA,uBAAa,EAACrD;IAErC,OAAQP;QACN,KAAK;YACH,OAAOX,YAAYiB,GAAG,CAAC1C,GAAG,CAACqB;QAC7B,KAAK;YACH,OAAQA;gBACN,KAAK;oBACH,OAAOI,YAAYgB,MAAM,CAACC,GAAG,IAAI;gBACnC,KAAK;oBACH,OAAOjB,YAAYgB,MAAM,CAACS,QAAQ,IAAI;gBACxC,KAAK;oBACH,OAAOzB,YAAYgB,MAAM,CAACwD,KAAK,IAAI;gBACrC;oBACE,OAAOxE,YAAYJ,IAAI,CAACrB,GAAG,CAACqB;YAChC;QACF,KAAK;YACH,OAAQA;gBACN,KAAK;oBACH,OAAOI,YAAYgB,MAAM,CAACyD,UAAU,IAAI;gBAC1C,KAAK;oBACH,OAAOzE,YAAYgB,MAAM,CAAC0D,eAAe,IAAI;gBAC/C;oBACE,OAAO;YACX;QACF,KAAK;YACH,IAAI,CAACJ,aAAa;gBAChB,OAAO;YACT;YAEA,OAAOA,YACJJ,cAAc,CAACtE,MACfoD,IAAI,CAAC,CAAC2B,UACLlH,oBAAoBuC,aAAa2E,SAASL;QAEhD;YAAS;gBACP,+DAA+D;gBAC/D,6DAA6D;gBAC7D,MAAMM,IAAWjE;gBACjB,OAAO;YACT;IACF;AACF;AA0BO,eAAerD,kBAAkB,EACtC0C,WAAW,EAEX6E,kBAAkB,EAElB9E,kBAAkB,EAClBE,cAAc,EACdE,WAAW,EACXG,SAAS,EACTX,GAAG,EAaJ;IACCkF,mBAAmB7D,MAAM,CAACC,GAAG,GAAGjB,YAAY8E,gBAAgB;IAC5DD,mBAAmB7D,MAAM,CAACS,QAAQ,GAAGzB,YAAY+E,qBAAqB;IACtEF,mBAAmB7D,MAAM,CAACwD,KAAK,GAAGxE,YAAYgF,kBAAkB;IAEhEH,mBAAmB7D,MAAM,CAAC0D,eAAe,GAAG1E,YAAY0E,eAAe;IAEvEG,mBAAmBjF,IAAI,CAACX,KAAK;IAC7B4F,mBAAmB5D,GAAG,CAAChC,KAAK;IAE5B,KAAK,MAAM,CAACY,UAAUC,MAAM,IAAIE,YAAYiF,MAAM,CAAE;QAClD,OAAQnF,MAAMa,IAAI;YAChB,KAAK;YACL,KAAK;gBACHkE,mBAAmBjF,IAAI,CAACP,GAAG,CAACQ,UAAUC;gBACtC;YACF,KAAK;gBAAY;oBACfA,MAAMwC,KAAK,CAAC4C,OAAO,CAAC,CAACtF;wBACnBiF,mBAAmB5D,GAAG,CAAC5B,GAAG,CAACO,KAAKuF,YAAY,EAAE;4BAC5CxE,MAAM;4BACN,GAAGf,IAAI;wBACT;oBACF;oBACA;gBACF;YACA,KAAK;gBAAa;oBAChBiF,mBAAmB5D,GAAG,CAAC5B,GAAG,CAACS,MAAMqF,YAAY,EAAErF;oBAC/C;gBACF;YACA;gBACElB,KAAIwG,IAAI,CAAC,CAAC,SAAS,EAAEvF,SAAS,EAAE,EAAEC,MAAMa,IAAI,CAAC,CAAC,CAAC;gBAC/C;QACJ;IACF;IAEA,IAAIhB,KAAK;QACP,MAAM0F,4BAA4B;YAChCtF;YACA8E;YAEA,GAAGlF,GAAG;QACR;IACF;IAEA,MAAM,EAAE8E,UAAU,EAAEC,eAAe,EAAE,GAAG1E;IAExC,8DAA8D;IAC9D,8DAA8D;IAC9D,sCAAsC;IACtC,IAAI6E,mBAAmB7D,MAAM,CAACyD,UAAU,IAAI,CAACA,YAAY;QACvD,MAAMvD,MAAML,IAAAA,qBAAW,EAAC,QAAQ,UAAU;QAC1C,wCAAwC;QACxC,OAAMlB,uBAAAA,IAAKU,KAAK,CAACiF,sBAAsB,CAACpE;QACxCnB,mBAAmBmC,MAAM,CAAChB;QAC1BvB,IAAIU,KAAK,CAACkF,OAAO,CAAC,cAAc;YAC9BpD,OAAOC,6CAA2B,CAACoD,kBAAkB;QACvD;IACF,OAAO,IAAI,CAACX,mBAAmB7D,MAAM,CAACyD,UAAU,IAAIA,YAAY;QAC9D,wCAAwC;QACxC9E,IAAIU,KAAK,CAACkF,OAAO,CAAC,cAAc;YAC9BpD,OAAOC,6CAA2B,CAACoD,kBAAkB;QACvD;IACF;IAEAX,mBAAmB7D,MAAM,CAACyD,UAAU,GAAGA;IAEvC,IAAIC,iBAAiB;QACnB,MAAMe,yBAAyB,OAC7BC,MACAC;YAEA,MAAMC,aAAa;gBACjBC,QAAQ;gBACRC,MAAM;YACR;YACA,MAAMC,iBAAiBpG,IAAIU,KAAK,CAAC2F,aAAa,CAC5C,CAAC,gBAAgB,EAAEJ,UAAU,CAACD,KAAK,EAAE,EACrCM,WACA;YAEF,MAAM/E,MAAML,IAAAA,qBAAW,EAAC,QAAQ,UAAU6E;YAE1C,MAAMvE,kBAAkB,MAAMuD,eAAe,CAACiB,KAAK,CAACvE,WAAW;YAC/DzB,IAAIU,KAAK,CAACgB,qBAAqB,CAACH,KAAKC,iBAAiB;YACtDG,IAAAA,oBAAa,EAACvB,oBAAoBmB,KAAKC,iBAAiB,OAAOb;YAC/DyF;QACF;QACA,MAAMN,uBAAuB,0BAA0B;QACvD,MAAMA,uBAAuB,wBAAwB;QACrD,MAAMxF,eAAe0B,sBAAsB,CACzC,mBACA;QAEF,MAAM1B,eAAe8B,cAAc,CAAC;YAClC5B;YACAC,oBAAoB6F;YACpBjG,aAAa6E;QACf;QAEAlF,IAAIuG,YAAY,CAACC,6BAA6B,GAAG;QACjD,MAAMxG,IAAIU,KAAK,CAAC+F,oBAAoB,CAClC,iCACAzG,IAAIuG,YAAY,CAACC,6BAA6B;IAElD,OAAO;QACLxG,IAAIuG,YAAY,CAACC,6BAA6B,GAAGF;QACjD,MAAMtG,IAAIU,KAAK,CAAC+F,oBAAoB,CAClC,iCACAzG,IAAIuG,YAAY,CAACC,6BAA6B;IAElD;IAEA,IAAI1B,YAAY;QACd,MAAMvD,MAAML,IAAAA,qBAAW,EAAC,QAAQ,UAAU;QAE1C,MAAM+B,WAAW6B,WAAW7B,QAAQ;QAEpC,eAAeyD;gBAWXpG;YAVF,MAAM8F,iBAAiBpG,IAAIU,KAAK,CAAC2F,aAAa,CAC5C,cACAC,WACA;YAEF,MAAM9E,kBAAkB,MAAMyB,SAASxB,WAAW;YAClDzB,IAAIU,KAAK,CAACgB,qBAAqB,CAACH,KAAKC,iBAAiB;YACtDG,IAAAA,oBAAa,EAACvB,oBAAoBmB,KAAKC,iBAAiB,OAAOb;YAC/D,MAAML,eAAe0B,sBAAsB,CAAC,cAAc;YAC1D,MAAM2E,oBACJrG,wCAAAA,eAAesG,qBAAqB,CAACrF,yBAArCjB,sCAA2CwE,UAAU,CAAC,IAAI;YAE5D,IAAI9E,OAAO2G,kBAAkB;gBAC3B3G,IAAIuG,YAAY,CAACzB,UAAU,GAAG;oBAC5B+B,OAAO;oBACP5G,MAAM;oBACN6G,UAAUH,iBAAiBG,QAAQ;gBACrC;YACF;YACAV;QACF;QACA,MAAMM;QAEN,IAAI1G,KAAK;YACPA,uBAAAA,IAAKU,KAAK,CAAC2B,kBAAkB,CAC3Bd,KACA,OACA0B,UACA;gBACE,MAAMmD,iBAAiBpG,IAAIU,KAAK,CAAC2F,aAAa,CAC5C,cACAC,WACA;gBAEF,MAAMI;gBACN,MAAM1G,IAAIU,KAAK,CAAC+F,oBAAoB,CAClC,wBACAzG,IAAIuG,YAAY,CAACQ,oBAAoB;gBAEvC,MAAM/G,IAAIU,KAAK,CAAC+F,oBAAoB,CAClC,cACAzG,IAAIuG,YAAY,CAACzB,UAAU;gBAE7B,MAAMxE,eAAe8B,cAAc,CAAC;oBAClC5B;oBACAC,oBAAoB6F;oBACpBjG,aAAa6E;gBACf;gBAEAkB,kCAAAA;gBACA,OAAO;oBAAE5D,OAAOC,6CAA2B,CAACoD,kBAAkB;gBAAC;YACjE,GACA;gBACE,OAAO;oBACLrD,OAAOC,6CAA2B,CAACoD,kBAAkB;gBACvD;YACF;QAEJ;IACF,OAAO;QACLvF,eAAe2B,wBAAwB,CACrCf,IAAAA,qBAAW,EAAC,QAAQ,UAAU;QAEhClB,IAAIuG,YAAY,CAACQ,oBAAoB,GAAGT;QACxCtG,IAAIuG,YAAY,CAACzB,UAAU,GAAGwB;IAChC;IAEA,MAAMtG,IAAIU,KAAK,CAAC+F,oBAAoB,CAClC,wBACAzG,IAAIuG,YAAY,CAACQ,oBAAoB;IAEvC,MAAM/G,IAAIU,KAAK,CAAC+F,oBAAoB,CAClC,cACAzG,IAAIuG,YAAY,CAACzB,UAAU;AAE/B;AAEA,eAAeY,4BAA4B,EACzCtF,kBAAkB,EAClB8E,kBAAkB,EAElBP,WAAW,EACXqC,mBAAmB,EACnBC,OAAO,EACPC,YAAY,EAEZxG,KAAK,EAIqB;IAC1B,yEAAyE;IACzE,KAAK,MAAMa,OAAOoD,YAAYF,IAAI,GAAI;QACpC,IAAI,CAAC3G,oBAAoBoH,oBAAoB3D,KAAKoD,cAAc;YAC9DA,YAAYpC,MAAM,CAAChB;QACrB;IACF;IAEA,KAAK,MAAMA,OAAOyF,oBAAoBvC,IAAI,GAAI;QAC5C,mCAAmC;QACnC,IAAI,CAAC3G,oBAAoBoH,oBAAoB3D,KAAKoD,cAAc;YAC9D,MAAMjE,MAAMiF,sBAAsB,CAACpE;QACrC;IACF;IAEA,KAAK,MAAM,CAACA,IAAI,IAAInB,mBAAoB;QACtC,IAAI,CAACtC,oBAAoBoH,oBAAoB3D,KAAKoD,cAAc;YAC9DvE,mBAAmBmC,MAAM,CAAChB;QAC5B;IACF;IAEA,KAAK,MAAM4F,UAAUF,QAAS;QAC5B,MAAMG,QAAQF,aAAahD,GAAG,CAACiD;QAC/B,IAAI,CAACC,OAAO;YACV;QACF;QAEA,KAAK,MAAM7F,OAAO6F,MAAMC,YAAY,CAAC5C,IAAI,GAAI;YAC3C,IAAI,CAAC3G,oBAAoBoH,oBAAoB3D,KAAKoD,cAAc;gBAC9DyC,MAAMC,YAAY,CAAC9E,MAAM,CAAChB;YAC5B;QACF;QAEA,KAAK,MAAM+F,MAAMF,MAAMG,aAAa,CAAC9C,IAAI,GAAI;YAC3C,IACE,CAAC3G,oBACCoH,oBACAhE,IAAAA,qBAAW,EAAC,UAAU,UAAUoG,KAChC3C,cAEF;gBACAjE,MAAM8G,wBAAwB,CAACL,QAAQG;YACzC;QACF;IACF;AACF;AAEO,eAAe1J,sBAAsB,EAC1CwC,kBAAkB,EAClBC,WAAW,EACXC,cAAc,EACdE,WAAW,EACXC,kBAAkB,EAClBE,SAAS,EACTD,KAAK,EASN;IACC,IAAIL,YAAYgB,MAAM,CAACC,GAAG,EAAE;QAC1B,MAAMC,MAAML,IAAAA,qBAAW,EAAC,SAAS,UAAU;QAE3C,MAAMM,kBAAkB,MAAMnB,YAAYgB,MAAM,CAACC,GAAG,CAACG,WAAW;QAChEf,MAAMgB,qBAAqB,CAACH,KAAKC,iBAAiB;QAClDd,MAAM2B,kBAAkB,CACtBd,KACA,OACAlB,YAAYgB,MAAM,CAACC,GAAG,EACtB;YACE,oEAAoE;YACpE,qIAAqI;YACrI,OAAO;gBAAEkB,OAAOC,6CAA2B,CAACO,cAAc;YAAC;QAC7D,GACA;YACE,OAAO;gBACLH,QAAQJ,6CAA2B,CAACK,WAAW;gBAC/CC,MAAM;YACR;QACF;QAEFpB,IAAAA,oBAAa,EAACvB,oBAAoBmB,KAAKC,iBAAiB,OAAOb;IACjE;IACA,MAAML,eAAesB,iBAAiB,CAAC;IACvC,MAAMtB,eAAeuB,iBAAiB,CAAC;IACvC,MAAMvB,eAAe4B,gBAAgB,CAAC;IAEtC,IAAI7B,YAAYgB,MAAM,CAACS,QAAQ,EAAE;QAC/B,MAAMP,MAAML,IAAAA,qBAAW,EAAC,SAAS,UAAU;QAE3C,MAAMM,kBAAkB,MAAMnB,YAAYgB,MAAM,CAACS,QAAQ,CAACL,WAAW;QACrEf,MAAMgB,qBAAqB,CAACH,KAAKC,iBAAiB;QAClDd,MAAM2B,kBAAkB,CACtBd,KACA,OACAlB,YAAYgB,MAAM,CAACS,QAAQ,EAC3B;YACE,OAAO;gBACLe,QAAQJ,6CAA2B,CAACK,WAAW;gBAC/CC,MAAM;YACR;QACF,GACA,CAACH;YACC,OAAO;gBACLC,QAAQJ,6CAA2B,CAACK,WAAW;gBAC/CC,MAAM,CAAC,+CAA+C,EAAEH,GAAG;YAC7D;QACF;QAEFjB,IAAAA,oBAAa,EAACvB,oBAAoBmB,KAAKC,iBAAiB,OAAOb;IACjE;IACA,MAAML,eAAeuB,iBAAiB,CAAC;IAEvC,IAAIxB,YAAYgB,MAAM,CAACwD,KAAK,EAAE;QAC5B,MAAMtD,MAAML,IAAAA,qBAAW,EAAC,SAAS,UAAU;QAE3C,MAAMM,kBAAkB,MAAMnB,YAAYgB,MAAM,CAACwD,KAAK,CAACpD,WAAW;QAClEf,MAAMgB,qBAAqB,CAACH,KAAKC,iBAAiB;QAClDd,MAAM2B,kBAAkB,CACtBd,KACA,OACAlB,YAAYgB,MAAM,CAACwD,KAAK,EACxB;YACE,oEAAoE;YACpE,qIAAqI;YACrI,OAAO;gBAAErC,OAAOC,6CAA2B,CAACO,cAAc;YAAC;QAC7D,GACA,CAACJ;YACC,OAAO;gBACLC,QAAQJ,6CAA2B,CAACK,WAAW;gBAC/CC,MAAM,CAAC,8BAA8B,EAAEH,GAAG;YAC5C;QACF;QAEFjB,IAAAA,oBAAa,EAACvB,oBAAoBmB,KAAKC,iBAAiB,OAAOb;IACjE;IACA,MAAML,eAAesB,iBAAiB,CAAC;IACvC,MAAMtB,eAAeuB,iBAAiB,CAAC;IACvC,MAAMvB,eAAe4B,gBAAgB,CAAC;IAEtC,MAAM5B,eAAe8B,cAAc,CAAC;QAClC5B;QACAC;QACAJ;IACF;AACF;AAEO,SAASlC,kBAAkBgC,KAAa;IAC7C,OAAOA,MAAMsH,OAAO,CAAC,YAAY;AACnC;AAEO,SAAS/J,eAAeyC,KAAa;IAC1C,OAAOA,QAAQ;AACjB;AAEO,SAAS1C,qBAAqB0C,KAAa;IAChD,OAAOA,QAAQ;AACjB;AAMO,SAASnC,wCACdmC,KAAa,EACbuH,GAAmB;IAEnB,IAAIC,gBAAgBxH;IACpB,IAAIyH,IAAAA,gCAAe,EAACD,gBAAgB;QAClCA,gBAAgBA,cAAcE,QAAQ,CAAC,YACnCF,cAAcG,KAAK,CAAC,GAAG,CAAC,SAASC,MAAM,IACvCJ;QAEJ,IAAID,KAAK;YACP,IAAIC,cAAcE,QAAQ,CAAC,uBAAuB;gBAChDF,gBAAgBA,cAAcG,KAAK,CAAC,GAAG,CAAC,qBAAqBC,MAAM;YACrE;YACA,IAAIJ,cAAcE,QAAQ,CAAC,mBAAmBH,QAAQ,QAAQ;gBAC5D,kDAAkD;gBAClDC,gBAAgBA,cAAcG,KAAK,CAAC,GAAG,CAAC,OAAOC,MAAM;YACvD;QACF;QACAJ,gBAAgBA,gBAAgB;IAClC;IACA,OAAOA;AACT"}
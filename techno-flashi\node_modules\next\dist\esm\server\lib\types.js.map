{"version": 3, "sources": ["../../../src/server/lib/types.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\n\nimport type { Duplex } from 'stream'\n\nexport type WorkerRequestHandler = (\n  req: IncomingMessage,\n  res: ServerResponse\n) => Promise<any>\n\nexport type WorkerUpgradeHandler = (\n  req: IncomingMessage,\n  socket: Duplex,\n  head: Buffer\n) => any\n"], "names": [], "mappings": "AASA,WAIQ"}
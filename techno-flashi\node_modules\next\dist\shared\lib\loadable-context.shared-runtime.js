'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "LoadableContext", {
    enumerable: true,
    get: function() {
        return LoadableContext;
    }
});
const _interop_require_default = require("@swc/helpers/_/_interop_require_default");
const _react = /*#__PURE__*/ _interop_require_default._(require("react"));
const LoadableContext = _react.default.createContext(null);
if (process.env.NODE_ENV !== 'production') {
    LoadableContext.displayName = 'LoadableContext';
}

//# sourceMappingURL=loadable-context.shared-runtime.js.map
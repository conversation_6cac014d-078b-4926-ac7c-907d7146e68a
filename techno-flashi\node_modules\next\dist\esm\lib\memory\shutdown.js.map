{"version": 3, "sources": ["../../../src/lib/memory/shutdown.ts"], "sourcesContent": ["import { info } from '../../build/output/log'\nimport { bold } from '../picocolors'\nimport { getGcEvents, stopObservingGc } from './gc-observer'\nimport { getAllMemoryUsageSpans, stopPeriodicMemoryUsageTracing } from './trace'\n\nexport function disableMemoryDebuggingMode(): void {\n  stopPeriodicMemoryUsageTracing()\n  stopObservingGc()\n\n  info(bold('Memory usage report:'))\n\n  const gcEvents = getGcEvents()\n  const totalTimeInGcMs = gcEvents.reduce(\n    (acc, event) => acc + event.duration,\n    0\n  )\n  info(` - Total time spent in GC: ${totalTimeInGcMs.toFixed(2)}ms`)\n\n  const allMemoryUsage = getAllMemoryUsageSpans()\n  const peakHeapUsage = Math.max(\n    ...allMemoryUsage.map((usage) => usage['memory.heapUsed'])\n  )\n  const peakRssUsage = Math.max(\n    ...allMemoryUsage.map((usage) => usage['memory.rss'])\n  )\n  info(` - Peak heap usage: ${(peakHeapUsage / 1024 / 1024).toFixed(2)} MB`)\n  info(` - Peak RSS usage: ${(peakRssUsage / 1024 / 1024).toFixed(2)} MB`)\n}\n"], "names": ["info", "bold", "getGcEvents", "stopObservingGc", "getAllMemoryUsageSpans", "stopPeriodicMemoryUsageTracing", "disableMemoryDebuggingMode", "gcEvents", "totalTimeInGcMs", "reduce", "acc", "event", "duration", "toFixed", "allMemoryUsage", "peakHeapUsage", "Math", "max", "map", "usage", "peakRssUsage"], "mappings": "AAAA,SAASA,IAAI,QAAQ,yBAAwB;AAC7C,SAASC,IAAI,QAAQ,gBAAe;AACpC,SAASC,WAAW,EAAEC,eAAe,QAAQ,gBAAe;AAC5D,SAASC,sBAAsB,EAAEC,8BAA8B,QAAQ,UAAS;AAEhF,OAAO,SAASC;IACdD;IACAF;IAEAH,KAAKC,KAAK;IAEV,MAAMM,WAAWL;IACjB,MAAMM,kBAAkBD,SAASE,MAAM,CACrC,CAACC,KAAKC,QAAUD,MAAMC,MAAMC,QAAQ,EACpC;IAEFZ,KAAK,CAAC,2BAA2B,EAAEQ,gBAAgBK,OAAO,CAAC,GAAG,EAAE,CAAC;IAEjE,MAAMC,iBAAiBV;IACvB,MAAMW,gBAAgBC,KAAKC,GAAG,IACzBH,eAAeI,GAAG,CAAC,CAACC,QAAUA,KAAK,CAAC,kBAAkB;IAE3D,MAAMC,eAAeJ,KAAKC,GAAG,IACxBH,eAAeI,GAAG,CAAC,CAACC,QAAUA,KAAK,CAAC,aAAa;IAEtDnB,KAAK,CAAC,oBAAoB,EAAE,AAACe,CAAAA,gBAAgB,OAAO,IAAG,EAAGF,OAAO,CAAC,GAAG,GAAG,CAAC;IACzEb,KAAK,CAAC,mBAAmB,EAAE,AAACoB,CAAAA,eAAe,OAAO,IAAG,EAAGP,OAAO,CAAC,GAAG,GAAG,CAAC;AACzE"}
{"version": 3, "sources": ["../../src/lib/url.ts"], "sourcesContent": ["import { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\n\nconst DUMMY_ORIGIN = 'http://n'\n\nexport function isFullStringUrl(url: string) {\n  return /https?:\\/\\//.test(url)\n}\n\nexport function parseUrl(url: string): URL | undefined {\n  let parsed = undefined\n  try {\n    parsed = new URL(url, DUMMY_ORIGIN)\n  } catch {}\n  return parsed\n}\n\nexport function stripNextRscUnionQuery(relativeUrl: string): string {\n  const urlInstance = new URL(relativeUrl, DUMMY_ORIGIN)\n  urlInstance.searchParams.delete(NEXT_RSC_UNION_QUERY)\n\n  return urlInstance.pathname + urlInstance.search\n}\n"], "names": ["NEXT_RSC_UNION_QUERY", "DUMMY_ORIGIN", "isFullStringUrl", "url", "test", "parseUrl", "parsed", "undefined", "URL", "stripNextRscUnionQuery", "relativeUrl", "urlInstance", "searchParams", "delete", "pathname", "search"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,0CAAyC;AAE9E,MAAMC,eAAe;AAErB,OAAO,SAASC,gBAAgBC,GAAW;IACzC,OAAO,cAAcC,IAAI,CAACD;AAC5B;AAEA,OAAO,SAASE,SAASF,GAAW;IAClC,IAAIG,SAASC;IACb,IAAI;QACFD,SAAS,IAAIE,IAAIL,KAAKF;IACxB,EAAE,OAAM,CAAC;IACT,OAAOK;AACT;AAEA,OAAO,SAASG,uBAAuBC,WAAmB;IACxD,MAAMC,cAAc,IAAIH,IAAIE,aAAaT;IACzCU,YAAYC,YAAY,CAACC,MAAM,CAACb;IAEhC,OAAOW,YAAYG,QAAQ,GAAGH,YAAYI,MAAM;AAClD"}
{"version": 3, "sources": ["../../../../src/server/lib/router-utils/setup-dev-bundler.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../../config-shared'\nimport type { FilesystemDynamicRoute } from './filesystem'\nimport type { UnwrapPromise } from '../../../lib/coalesced-function'\nimport {\n  getPageStaticInfo,\n  type MiddlewareMatcher,\n} from '../../../build/analysis/get-page-static-info'\nimport type { MiddlewareRouteMatch } from '../../../shared/lib/router/utils/middleware-route-matcher'\nimport type { PropagateToWorkersField } from './types'\nimport type { NextJsHotReloaderInterface } from '../../dev/hot-reloader-types'\n\nimport { createDefineEnv } from '../../../build/swc'\nimport fs from 'fs'\nimport { mkdir } from 'fs/promises'\nimport url from 'url'\nimport path from 'path'\nimport qs from 'querystring'\nimport Watchpack from 'next/dist/compiled/watchpack'\nimport { loadEnvConfig } from '@next/env'\nimport findUp from 'next/dist/compiled/find-up'\nimport { buildCustomRoute } from './filesystem'\nimport * as Log from '../../../build/output/log'\nimport HotReloaderWebpack from '../../dev/hot-reloader-webpack'\nimport { setGlobal } from '../../../trace/shared'\nimport type { Telemetry } from '../../../telemetry/storage'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport loadJsConfig from '../../../build/load-jsconfig'\nimport { createValidFileMatcher } from '../find-page-file'\nimport {\n  EVENT_BUILD_FEATURE_USAGE,\n  eventCliSession,\n} from '../../../telemetry/events'\nimport { getDefineEnv } from '../../../build/webpack/plugins/define-env-plugin'\nimport { getSortedRoutes } from '../../../shared/lib/router/utils'\nimport {\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n} from '../../../build/entries'\nimport { verifyTypeScriptSetup } from '../../../lib/verify-typescript-setup'\nimport { verifyPartytownSetup } from '../../../lib/verify-partytown-setup'\nimport { getRouteRegex } from '../../../shared/lib/router/utils/route-regex'\nimport { normalizeAppPath } from '../../../shared/lib/router/utils/app-paths'\nimport { buildDataRoute } from './build-data-route'\nimport { getRouteMatcher } from '../../../shared/lib/router/utils/route-matcher'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\nimport { createClientRouterFilter } from '../../../lib/create-client-router-filter'\nimport { absolutePathToPage } from '../../../shared/lib/page-path/absolute-path-to-page'\nimport { generateInterceptionRoutesRewrites } from '../../../lib/generate-interception-routes-rewrites'\n\nimport {\n  CLIENT_STATIC_FILES_PATH,\n  DEV_CLIENT_PAGES_MANIFEST,\n  DEV_CLIENT_MIDDLEWARE_MANIFEST,\n  PHASE_DEVELOPMENT_SERVER,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../../../shared/lib/constants'\n\nimport { getMiddlewareRouteMatcher } from '../../../shared/lib/router/utils/middleware-route-matcher'\n\nimport {\n  isMiddlewareFile,\n  NestedMiddlewareError,\n  isInstrumentationHookFile,\n  getPossibleMiddlewareFilenames,\n  getPossibleInstrumentationHookFilenames,\n} from '../../../build/utils'\nimport { devPageFiles } from '../../../build/webpack/plugins/next-types-plugin/shared'\nimport type { LazyRenderServerInstance } from '../router-server'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../dev/hot-reloader-types'\nimport { PAGE_TYPES } from '../../../lib/page-types'\nimport { createHotReloaderTurbopack } from '../../dev/hot-reloader-turbopack'\nimport { generateEncryptionKeyBase64 } from '../../app-render/encryption-utils-server'\nimport { isMetadataRouteFile } from '../../../lib/metadata/is-metadata-route'\nimport { normalizeMetadataPageToRoute } from '../../../lib/metadata/get-metadata-route'\nimport { createEnvDefinitions } from '../experimental/create-env-definitions'\nimport { JsConfigPathsPlugin } from '../../../build/webpack/plugins/jsconfig-paths-plugin'\nimport { store as consoleStore } from '../../../build/output/store'\nimport {\n  isPersistentCachingEnabled,\n  ModuleBuildError,\n  TurbopackInternalError,\n} from '../../../shared/lib/turbopack/utils'\n\nexport type SetupOpts = {\n  renderServer: LazyRenderServerInstance\n  dir: string\n  turbo?: boolean\n  appDir?: string\n  pagesDir?: string\n  telemetry: Telemetry\n  isCustomServer?: boolean\n  fsChecker: UnwrapPromise<\n    ReturnType<typeof import('./filesystem').setupFsCheck>\n  >\n  nextConfig: NextConfigComplete\n  port: number\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  resetFetch: () => void\n}\n\nexport type ServerFields = {\n  actualMiddlewareFile?: string | undefined\n  actualInstrumentationHookFile?: string | undefined\n  appPathRoutes?: Record<string, string | string[]>\n  middleware?:\n    | {\n        page: string\n        match: MiddlewareRouteMatch\n        matchers?: MiddlewareMatcher[]\n      }\n    | undefined\n  hasAppNotFound?: boolean\n  interceptionRoutes?: ReturnType<\n    typeof import('./filesystem').buildCustomRoute\n  >[]\n  setIsrStatus?: (key: string, value: boolean) => void\n  resetFetch?: () => void\n}\n\nasync function verifyTypeScript(opts: SetupOpts) {\n  let usingTypeScript = false\n  const verifyResult = await verifyTypeScriptSetup({\n    dir: opts.dir,\n    distDir: opts.nextConfig.distDir,\n    intentDirs: [opts.pagesDir, opts.appDir].filter(Boolean) as string[],\n    typeCheckPreflight: false,\n    tsconfigPath: opts.nextConfig.typescript.tsconfigPath,\n    disableStaticImages: opts.nextConfig.images.disableStaticImages,\n    hasAppDir: !!opts.appDir,\n    hasPagesDir: !!opts.pagesDir,\n  })\n\n  if (verifyResult.version) {\n    usingTypeScript = true\n  }\n  return usingTypeScript\n}\n\nexport async function propagateServerField(\n  opts: SetupOpts,\n  field: PropagateToWorkersField,\n  args: any\n) {\n  await opts.renderServer?.instance?.propagateServerField(opts.dir, field, args)\n}\n\nasync function startWatcher(opts: SetupOpts) {\n  const { nextConfig, appDir, pagesDir, dir, resetFetch } = opts\n  const { useFileSystemPublicRoutes } = nextConfig\n  const usingTypeScript = await verifyTypeScript(opts)\n\n  const distDir = path.join(opts.dir, opts.nextConfig.distDir)\n\n  // we ensure the types directory exists here\n  if (usingTypeScript) {\n    const distTypesDir = path.join(distDir, 'types')\n    if (!fs.existsSync(distTypesDir)) {\n      await mkdir(distTypesDir, { recursive: true })\n    }\n  }\n\n  setGlobal('distDir', distDir)\n  setGlobal('phase', PHASE_DEVELOPMENT_SERVER)\n\n  const validFileMatcher = createValidFileMatcher(\n    nextConfig.pageExtensions,\n    appDir\n  )\n\n  const serverFields: ServerFields = {}\n\n  // Update logging state once based on next.config.js when initializing\n  consoleStore.setState({\n    logging: nextConfig.logging !== false,\n  })\n\n  const hotReloader: NextJsHotReloaderInterface = opts.turbo\n    ? await createHotReloaderTurbopack(opts, serverFields, distDir, resetFetch)\n    : new HotReloaderWebpack(opts.dir, {\n        appDir,\n        pagesDir,\n        distDir,\n        config: opts.nextConfig,\n        buildId: 'development',\n        encryptionKey: await generateEncryptionKeyBase64({\n          isBuild: false,\n          distDir,\n        }),\n        telemetry: opts.telemetry,\n        rewrites: opts.fsChecker.rewrites,\n        previewProps: opts.fsChecker.prerenderManifest.preview,\n        resetFetch,\n      })\n\n  await hotReloader.start()\n\n  if (opts.nextConfig.experimental.nextScriptWorkers) {\n    await verifyPartytownSetup(\n      opts.dir,\n      path.join(distDir, CLIENT_STATIC_FILES_PATH)\n    )\n  }\n\n  opts.fsChecker.ensureCallback(async function ensure(item) {\n    if (item.type === 'appFile' || item.type === 'pageFile') {\n      await hotReloader.ensurePage({\n        clientOnly: false,\n        page: item.itemPath,\n        isApp: item.type === 'appFile',\n        definition: undefined,\n      })\n    }\n  })\n\n  let resolved = false\n  let prevSortedRoutes: string[] = []\n\n  await new Promise<void>(async (resolve, reject) => {\n    if (pagesDir) {\n      // Watchpack doesn't emit an event for an empty directory\n      fs.readdir(pagesDir, (_, files) => {\n        if (files?.length) {\n          return\n        }\n\n        if (!resolved) {\n          resolve()\n          resolved = true\n        }\n      })\n    }\n\n    const pages = pagesDir ? [pagesDir] : []\n    const app = appDir ? [appDir] : []\n    const directories = [...pages, ...app]\n\n    const rootDir = pagesDir || appDir\n    const files = [\n      ...getPossibleMiddlewareFilenames(\n        path.join(rootDir!, '..'),\n        nextConfig.pageExtensions\n      ),\n      ...getPossibleInstrumentationHookFilenames(\n        path.join(rootDir!, '..'),\n        nextConfig.pageExtensions\n      ),\n    ]\n    let nestedMiddleware: string[] = []\n\n    const envFiles = [\n      '.env.development.local',\n      '.env.local',\n      '.env.development',\n      '.env',\n    ].map((file) => path.join(dir, file))\n\n    files.push(...envFiles)\n\n    // tsconfig/jsconfig paths hot-reloading\n    const tsconfigPaths = [\n      path.join(dir, 'tsconfig.json'),\n      path.join(dir, 'jsconfig.json'),\n    ] as const\n    files.push(...tsconfigPaths)\n\n    const wp = new Watchpack({\n      ignored: (pathname: string) => {\n        return (\n          !files.some((file) => file.startsWith(pathname)) &&\n          !directories.some(\n            (d) => pathname.startsWith(d) || d.startsWith(pathname)\n          )\n        )\n      },\n    })\n    const fileWatchTimes = new Map()\n    let enabledTypeScript = usingTypeScript\n    let previousClientRouterFilters: any\n    let previousConflictingPagePaths: Set<string> = new Set()\n\n    wp.on('aggregated', async () => {\n      let middlewareMatchers: MiddlewareMatcher[] | undefined\n      const routedPages: string[] = []\n      const knownFiles = wp.getTimeInfoEntries()\n      const appPaths: Record<string, string[]> = {}\n      const pageNameSet = new Set<string>()\n      const conflictingAppPagePaths = new Set<string>()\n      const appPageFilePaths = new Map<string, string>()\n      const pagesPageFilePaths = new Map<string, string>()\n\n      let envChange = false\n      let tsconfigChange = false\n      let conflictingPageChange = 0\n      let hasRootAppNotFound = false\n\n      const { appFiles, pageFiles } = opts.fsChecker\n\n      appFiles.clear()\n      pageFiles.clear()\n      devPageFiles.clear()\n\n      const sortedKnownFiles: string[] = [...knownFiles.keys()].sort(\n        sortByPageExts(nextConfig.pageExtensions)\n      )\n\n      for (const fileName of sortedKnownFiles) {\n        if (\n          !files.includes(fileName) &&\n          !directories.some((d) => fileName.startsWith(d))\n        ) {\n          continue\n        }\n        const meta = knownFiles.get(fileName)\n\n        const watchTime = fileWatchTimes.get(fileName)\n        // If the file is showing up for the first time or the meta.timestamp is changed since last time\n        const watchTimeChange =\n          watchTime === undefined ||\n          (watchTime && watchTime !== meta?.timestamp)\n        fileWatchTimes.set(fileName, meta?.timestamp)\n\n        if (envFiles.includes(fileName)) {\n          if (watchTimeChange) {\n            envChange = true\n          }\n          continue\n        }\n\n        if (tsconfigPaths.includes(fileName)) {\n          if (fileName.endsWith('tsconfig.json')) {\n            enabledTypeScript = true\n          }\n          if (watchTimeChange) {\n            tsconfigChange = true\n          }\n          continue\n        }\n\n        if (\n          meta?.accuracy === undefined ||\n          !validFileMatcher.isPageFile(fileName)\n        ) {\n          continue\n        }\n\n        const isAppPath = Boolean(\n          appDir &&\n            normalizePathSep(fileName).startsWith(\n              normalizePathSep(appDir) + '/'\n            )\n        )\n        const isPagePath = Boolean(\n          pagesDir &&\n            normalizePathSep(fileName).startsWith(\n              normalizePathSep(pagesDir) + '/'\n            )\n        )\n\n        const rootFile = absolutePathToPage(fileName, {\n          dir: dir,\n          extensions: nextConfig.pageExtensions,\n          keepIndex: false,\n          pagesType: PAGE_TYPES.ROOT,\n        })\n\n        if (isMiddlewareFile(rootFile)) {\n          const staticInfo = await getStaticInfoIncludingLayouts({\n            pageFilePath: fileName,\n            config: nextConfig,\n            appDir: appDir,\n            page: rootFile,\n            isDev: true,\n            isInsideAppDir: isAppPath,\n            pageExtensions: nextConfig.pageExtensions,\n          })\n          if (nextConfig.output === 'export') {\n            Log.error(\n              'Middleware cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n            continue\n          }\n          serverFields.actualMiddlewareFile = rootFile\n          await propagateServerField(\n            opts,\n            'actualMiddlewareFile',\n            serverFields.actualMiddlewareFile\n          )\n          middlewareMatchers = staticInfo.middleware?.matchers || [\n            { regexp: '.*', originalSource: '/:path*' },\n          ]\n          continue\n        }\n        if (isInstrumentationHookFile(rootFile)) {\n          serverFields.actualInstrumentationHookFile = rootFile\n          await propagateServerField(\n            opts,\n            'actualInstrumentationHookFile',\n            serverFields.actualInstrumentationHookFile\n          )\n          continue\n        }\n\n        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {\n          enabledTypeScript = true\n        }\n\n        if (!(isAppPath || isPagePath)) {\n          continue\n        }\n\n        // Collect all current filenames for the TS plugin to use\n        devPageFiles.add(fileName)\n\n        let pageName = absolutePathToPage(fileName, {\n          dir: isAppPath ? appDir! : pagesDir!,\n          extensions: nextConfig.pageExtensions,\n          keepIndex: isAppPath,\n          pagesType: isAppPath ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n        })\n\n        if (\n          isAppPath &&\n          appDir &&\n          isMetadataRouteFile(\n            fileName.replace(appDir, ''),\n            nextConfig.pageExtensions,\n            true\n          )\n        ) {\n          const staticInfo = await getPageStaticInfo({\n            pageFilePath: fileName,\n            nextConfig: {},\n            page: pageName,\n            isDev: true,\n            pageType: PAGE_TYPES.APP,\n          })\n\n          pageName = normalizeMetadataPageToRoute(\n            pageName,\n            !!(staticInfo.generateSitemaps || staticInfo.generateImageMetadata)\n          )\n        }\n\n        if (\n          !isAppPath &&\n          pageName.startsWith('/api/') &&\n          nextConfig.output === 'export'\n        ) {\n          Log.error(\n            'API Routes cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n          )\n          continue\n        }\n\n        if (isAppPath) {\n          const isRootNotFound = validFileMatcher.isRootNotFound(fileName)\n          hasRootAppNotFound = true\n\n          if (isRootNotFound) {\n            continue\n          }\n          if (!isRootNotFound && !validFileMatcher.isAppRouterPage(fileName)) {\n            continue\n          }\n          // Ignore files/directories starting with `_` in the app directory\n          if (normalizePathSep(pageName).includes('/_')) {\n            continue\n          }\n\n          const originalPageName = pageName\n          pageName = normalizeAppPath(pageName).replace(/%5F/g, '_')\n          if (!appPaths[pageName]) {\n            appPaths[pageName] = []\n          }\n          appPaths[pageName].push(originalPageName)\n\n          if (useFileSystemPublicRoutes) {\n            appFiles.add(pageName)\n          }\n\n          if (routedPages.includes(pageName)) {\n            continue\n          }\n        } else {\n          if (useFileSystemPublicRoutes) {\n            pageFiles.add(pageName)\n            // always add to nextDataRoutes for now but in future only add\n            // entries that actually use getStaticProps/getServerSideProps\n            opts.fsChecker.nextDataRoutes.add(pageName)\n          }\n        }\n        ;(isAppPath ? appPageFilePaths : pagesPageFilePaths).set(\n          pageName,\n          fileName\n        )\n\n        if (appDir && pageNameSet.has(pageName)) {\n          conflictingAppPagePaths.add(pageName)\n        } else {\n          pageNameSet.add(pageName)\n        }\n\n        /**\n         * If there is a middleware that is not declared in the root we will\n         * warn without adding it so it doesn't make its way into the system.\n         */\n        if (/[\\\\\\\\/]_middleware$/.test(pageName)) {\n          nestedMiddleware.push(pageName)\n          continue\n        }\n\n        routedPages.push(pageName)\n      }\n\n      const numConflicting = conflictingAppPagePaths.size\n      conflictingPageChange = numConflicting - previousConflictingPagePaths.size\n\n      if (conflictingPageChange !== 0) {\n        if (numConflicting > 0) {\n          let errorMessage = `Conflicting app and page file${\n            numConflicting === 1 ? ' was' : 's were'\n          } found, please remove the conflicting files to continue:\\n`\n\n          for (const p of conflictingAppPagePaths) {\n            const appPath = path.relative(dir, appPageFilePaths.get(p)!)\n            const pagesPath = path.relative(dir, pagesPageFilePaths.get(p)!)\n            errorMessage += `  \"${pagesPath}\" - \"${appPath}\"\\n`\n          }\n          hotReloader.setHmrServerError(new Error(errorMessage))\n        } else if (numConflicting === 0) {\n          hotReloader.clearHmrServerError()\n          await propagateServerField(opts, 'reloadMatchers', undefined)\n        }\n      }\n\n      previousConflictingPagePaths = conflictingAppPagePaths\n\n      let clientRouterFilters: any\n      if (nextConfig.experimental.clientRouterFilter) {\n        clientRouterFilters = createClientRouterFilter(\n          Object.keys(appPaths),\n          nextConfig.experimental.clientRouterFilterRedirects\n            ? ((nextConfig as any)._originalRedirects || []).filter(\n                (r: any) => !r.internal\n              )\n            : [],\n          nextConfig.experimental.clientRouterFilterAllowedRate\n        )\n\n        if (\n          !previousClientRouterFilters ||\n          JSON.stringify(previousClientRouterFilters) !==\n            JSON.stringify(clientRouterFilters)\n        ) {\n          envChange = true\n          previousClientRouterFilters = clientRouterFilters\n        }\n      }\n\n      if (!usingTypeScript && enabledTypeScript) {\n        // we tolerate the error here as this is best effort\n        // and the manual install command will be shown\n        await verifyTypeScript(opts)\n          .then(() => {\n            tsconfigChange = true\n          })\n          .catch(() => {})\n      }\n\n      if (envChange || tsconfigChange) {\n        if (envChange) {\n          const { loadedEnvFiles } = loadEnvConfig(\n            dir,\n            process.env.NODE_ENV === 'development',\n            Log,\n            true,\n            (envFilePath) => {\n              Log.info(`Reload env: ${envFilePath}`)\n            }\n          )\n\n          if (usingTypeScript && nextConfig.experimental?.typedEnv) {\n            // do not await, this is not essential for further process\n            createEnvDefinitions({\n              distDir,\n              loadedEnvFiles: [\n                ...loadedEnvFiles,\n                {\n                  path: nextConfig.configFileName,\n                  env: nextConfig.env,\n                  contents: '',\n                },\n              ],\n            })\n          }\n\n          await propagateServerField(opts, 'loadEnvConfig', [\n            { dev: true, forceReload: true, silent: true },\n          ])\n        }\n        let tsconfigResult:\n          | UnwrapPromise<ReturnType<typeof loadJsConfig>>\n          | undefined\n\n        if (tsconfigChange) {\n          try {\n            tsconfigResult = await loadJsConfig(dir, nextConfig)\n          } catch (_) {\n            /* do we want to log if there are syntax errors in tsconfig while editing? */\n          }\n        }\n\n        if (hotReloader.turbopackProject) {\n          const hasRewrites =\n            opts.fsChecker.rewrites.afterFiles.length > 0 ||\n            opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n            opts.fsChecker.rewrites.fallback.length > 0\n\n          await hotReloader.turbopackProject.update({\n            defineEnv: createDefineEnv({\n              isTurbopack: true,\n              clientRouterFilters,\n              config: nextConfig,\n              dev: true,\n              distDir,\n              fetchCacheKeyPrefix:\n                opts.nextConfig.experimental.fetchCacheKeyPrefix,\n              hasRewrites,\n              // TODO: Implement\n              middlewareMatchers: undefined,\n            }),\n          })\n        }\n\n        hotReloader.activeWebpackConfigs?.forEach((config, idx) => {\n          const isClient = idx === 0\n          const isNodeServer = idx === 1\n          const isEdgeServer = idx === 2\n          const hasRewrites =\n            opts.fsChecker.rewrites.afterFiles.length > 0 ||\n            opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n            opts.fsChecker.rewrites.fallback.length > 0\n\n          if (tsconfigChange) {\n            config.resolve?.plugins?.forEach((plugin: any) => {\n              // look for the JsConfigPathsPlugin and update with\n              // the latest paths/baseUrl config\n              if (plugin instanceof JsConfigPathsPlugin && tsconfigResult) {\n                const { resolvedBaseUrl, jsConfig } = tsconfigResult\n                const currentResolvedBaseUrl = plugin.resolvedBaseUrl\n                const resolvedUrlIndex = config.resolve?.modules?.findIndex(\n                  (item) => item === currentResolvedBaseUrl?.baseUrl\n                )\n\n                if (resolvedBaseUrl) {\n                  if (\n                    resolvedBaseUrl.baseUrl !== currentResolvedBaseUrl?.baseUrl\n                  ) {\n                    // remove old baseUrl and add new one\n                    if (resolvedUrlIndex && resolvedUrlIndex > -1) {\n                      config.resolve?.modules?.splice(resolvedUrlIndex, 1)\n                    }\n\n                    // If the resolvedBaseUrl is implicit we only remove the previous value.\n                    // Only add the baseUrl if it's explicitly set in tsconfig/jsconfig\n                    if (!resolvedBaseUrl.isImplicit) {\n                      config.resolve?.modules?.push(resolvedBaseUrl.baseUrl)\n                    }\n                  }\n                }\n\n                if (jsConfig?.compilerOptions?.paths && resolvedBaseUrl) {\n                  Object.keys(plugin.paths).forEach((key) => {\n                    delete plugin.paths[key]\n                  })\n                  Object.assign(plugin.paths, jsConfig.compilerOptions.paths)\n                  plugin.resolvedBaseUrl = resolvedBaseUrl\n                }\n              }\n            })\n          }\n\n          if (envChange) {\n            config.plugins?.forEach((plugin: any) => {\n              // we look for the DefinePlugin definitions so we can\n              // update them on the active compilers\n              if (\n                plugin &&\n                typeof plugin.definitions === 'object' &&\n                plugin.definitions.__NEXT_DEFINE_ENV\n              ) {\n                const newDefine = getDefineEnv({\n                  isTurbopack: false,\n                  clientRouterFilters,\n                  config: nextConfig,\n                  dev: true,\n                  distDir,\n                  fetchCacheKeyPrefix:\n                    opts.nextConfig.experimental.fetchCacheKeyPrefix,\n                  hasRewrites,\n                  isClient,\n                  isEdgeServer,\n                  isNodeOrEdgeCompilation: isNodeServer || isEdgeServer,\n                  isNodeServer,\n                  middlewareMatchers: undefined,\n                })\n\n                Object.keys(plugin.definitions).forEach((key) => {\n                  if (!(key in newDefine)) {\n                    delete plugin.definitions[key]\n                  }\n                })\n                Object.assign(plugin.definitions, newDefine)\n              }\n            })\n          }\n        })\n        await hotReloader.invalidate({\n          reloadAfterInvalidation: envChange,\n        })\n      }\n\n      if (nestedMiddleware.length > 0) {\n        Log.error(\n          new NestedMiddlewareError(\n            nestedMiddleware,\n            dir,\n            (pagesDir || appDir)!\n          ).message\n        )\n        nestedMiddleware = []\n      }\n\n      // Make sure to sort parallel routes to make the result deterministic.\n      serverFields.appPathRoutes = Object.fromEntries(\n        Object.entries(appPaths).map(([k, v]) => [k, v.sort()])\n      )\n      await propagateServerField(\n        opts,\n        'appPathRoutes',\n        serverFields.appPathRoutes\n      )\n\n      // TODO: pass this to fsChecker/next-dev-server?\n      serverFields.middleware = middlewareMatchers\n        ? {\n            match: null as any,\n            page: '/',\n            matchers: middlewareMatchers,\n          }\n        : undefined\n\n      await propagateServerField(opts, 'middleware', serverFields.middleware)\n      serverFields.hasAppNotFound = hasRootAppNotFound\n\n      opts.fsChecker.middlewareMatcher = serverFields.middleware?.matchers\n        ? getMiddlewareRouteMatcher(serverFields.middleware?.matchers)\n        : undefined\n\n      const interceptionRoutes = generateInterceptionRoutesRewrites(\n        Object.keys(appPaths),\n        opts.nextConfig.basePath\n      ).map((item) =>\n        buildCustomRoute(\n          'before_files_rewrite',\n          item,\n          opts.nextConfig.basePath,\n          opts.nextConfig.experimental.caseSensitiveRoutes\n        )\n      )\n\n      opts.fsChecker.rewrites.beforeFiles.push(...interceptionRoutes)\n\n      const exportPathMap =\n        (typeof nextConfig.exportPathMap === 'function' &&\n          (await nextConfig.exportPathMap?.(\n            {},\n            {\n              dev: true,\n              dir: opts.dir,\n              outDir: null,\n              distDir: distDir,\n              buildId: 'development',\n            }\n          ))) ||\n        {}\n\n      const exportPathMapEntries = Object.entries(exportPathMap || {})\n\n      if (exportPathMapEntries.length > 0) {\n        opts.fsChecker.exportPathMapRoutes = exportPathMapEntries.map(\n          ([key, value]) =>\n            buildCustomRoute(\n              'before_files_rewrite',\n              {\n                source: key,\n                destination: `${value.page}${\n                  value.query ? '?' : ''\n                }${qs.stringify(value.query)}`,\n              },\n              opts.nextConfig.basePath,\n              opts.nextConfig.experimental.caseSensitiveRoutes\n            )\n        )\n      }\n\n      try {\n        // we serve a separate manifest with all pages for the client in\n        // dev mode so that we can match a page after a rewrite on the client\n        // before it has been built and is populated in the _buildManifest\n        const sortedRoutes = getSortedRoutes(routedPages)\n\n        opts.fsChecker.dynamicRoutes = sortedRoutes.map(\n          (page): FilesystemDynamicRoute => {\n            const regex = getRouteRegex(page)\n            return {\n              regex: regex.re.toString(),\n              match: getRouteMatcher(regex),\n              page,\n            }\n          }\n        )\n\n        const dataRoutes: typeof opts.fsChecker.dynamicRoutes = []\n\n        for (const page of sortedRoutes) {\n          const route = buildDataRoute(page, 'development')\n          const routeRegex = getRouteRegex(route.page)\n          dataRoutes.push({\n            ...route,\n            regex: routeRegex.re.toString(),\n            match: getRouteMatcher({\n              // TODO: fix this in the manifest itself, must also be fixed in\n              // upstream builder that relies on this\n              re: opts.nextConfig.i18n\n                ? new RegExp(\n                    route.dataRouteRegex.replace(\n                      `/development/`,\n                      `/development/(?<nextLocale>[^/]+?)/`\n                    )\n                  )\n                : new RegExp(route.dataRouteRegex),\n              groups: routeRegex.groups,\n            }),\n          })\n        }\n        opts.fsChecker.dynamicRoutes.unshift(...dataRoutes)\n\n        if (!prevSortedRoutes?.every((val, idx) => val === sortedRoutes[idx])) {\n          const addedRoutes = sortedRoutes.filter(\n            (route) => !prevSortedRoutes.includes(route)\n          )\n          const removedRoutes = prevSortedRoutes.filter(\n            (route) => !sortedRoutes.includes(route)\n          )\n\n          // emit the change so clients fetch the update\n          hotReloader.send({\n            action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE,\n            data: [\n              {\n                devPagesManifest: true,\n              },\n            ],\n          })\n\n          addedRoutes.forEach((route) => {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE,\n              data: [route],\n            })\n          })\n\n          removedRoutes.forEach((route) => {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE,\n              data: [route],\n            })\n          })\n        }\n        prevSortedRoutes = sortedRoutes\n\n        if (!resolved) {\n          resolve()\n          resolved = true\n        }\n      } catch (e) {\n        if (!resolved) {\n          reject(e)\n          resolved = true\n        } else {\n          Log.warn('Failed to reload dynamic routes:', e)\n        }\n      } finally {\n        // Reload the matchers. The filesystem would have been written to,\n        // and the matchers need to re-scan it to update the router.\n        await propagateServerField(opts, 'reloadMatchers', undefined)\n      }\n    })\n\n    wp.watch({ directories: [dir], startTime: 0 })\n  })\n\n  const clientPagesManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${DEV_CLIENT_PAGES_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(clientPagesManifestPath)\n\n  const devMiddlewareManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${DEV_CLIENT_MIDDLEWARE_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(devMiddlewareManifestPath)\n\n  const devTurbopackMiddlewareManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(devTurbopackMiddlewareManifestPath)\n\n  async function requestHandler(req: IncomingMessage, res: ServerResponse) {\n    const parsedUrl = url.parse(req.url || '/')\n\n    if (parsedUrl.pathname?.includes(clientPagesManifestPath)) {\n      res.statusCode = 200\n      res.setHeader('Content-Type', 'application/json; charset=utf-8')\n      res.end(\n        JSON.stringify({\n          pages: prevSortedRoutes.filter(\n            (route) => !opts.fsChecker.appFiles.has(route)\n          ),\n        })\n      )\n      return { finished: true }\n    }\n\n    if (\n      parsedUrl.pathname?.includes(devMiddlewareManifestPath) ||\n      parsedUrl.pathname?.includes(devTurbopackMiddlewareManifestPath)\n    ) {\n      res.statusCode = 200\n      res.setHeader('Content-Type', 'application/json; charset=utf-8')\n      res.end(JSON.stringify(serverFields.middleware?.matchers || []))\n      return { finished: true }\n    }\n    return { finished: false }\n  }\n\n  function logErrorWithOriginalStack(\n    err: unknown,\n    type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ) {\n    if (err instanceof ModuleBuildError) {\n      // Errors that may come from issues from the user's code\n      Log.error(err.message)\n    } else if (err instanceof TurbopackInternalError) {\n      // An internal Turbopack error that has been handled by next-swc, written\n      // to disk and a simplified message shown to user on the Rust side.\n    } else if (type === 'warning') {\n      Log.warn(err)\n    } else if (type === 'app-dir') {\n      Log.error(err)\n    } else if (type) {\n      Log.error(`${type}:`, err)\n    } else {\n      Log.error(err)\n    }\n  }\n\n  return {\n    serverFields,\n    hotReloader,\n    requestHandler,\n    logErrorWithOriginalStack,\n\n    async ensureMiddleware(requestUrl?: string) {\n      if (!serverFields.actualMiddlewareFile) return\n      return hotReloader.ensurePage({\n        page: serverFields.actualMiddlewareFile,\n        clientOnly: false,\n        definition: undefined,\n        url: requestUrl,\n      })\n    },\n  }\n}\n\nexport async function setupDevBundler(opts: SetupOpts) {\n  const isSrcDir = path\n    .relative(opts.dir, opts.pagesDir || opts.appDir || '')\n    .startsWith('src')\n\n  const result = await startWatcher(opts)\n\n  opts.telemetry.record(\n    eventCliSession(\n      path.join(opts.dir, opts.nextConfig.distDir),\n      opts.nextConfig,\n      {\n        webpackVersion: 5,\n        isSrcDir,\n        turboFlag: !!opts.turbo,\n        cliCommand: 'dev',\n        appDir: !!opts.appDir,\n        pagesDir: !!opts.pagesDir,\n        isCustomServer: !!opts.isCustomServer,\n        hasNowJson: !!(await findUp('now.json', { cwd: opts.dir })),\n      }\n    )\n  )\n\n  // Track build features for dev server here:\n  opts.telemetry.record({\n    eventName: EVENT_BUILD_FEATURE_USAGE,\n    payload: {\n      featureName: 'turbopackPersistentCaching',\n      invocationCount: isPersistentCachingEnabled(opts.nextConfig) ? 1 : 0,\n    },\n  })\n\n  return result\n}\n\nexport type DevBundler = Awaited<ReturnType<typeof setupDevBundler>>\n\n// Returns a trace rewritten through Turbopack's sourcemaps\n"], "names": ["getPageStaticInfo", "createDefineEnv", "fs", "mkdir", "url", "path", "qs", "Watchpack", "loadEnvConfig", "findUp", "buildCustomRoute", "Log", "HotReloaderWebpack", "setGlobal", "loadJsConfig", "createValidFileMatcher", "EVENT_BUILD_FEATURE_USAGE", "eventCliSession", "getDefineEnv", "getSortedRoutes", "getStaticInfoIncludingLayouts", "sortByPageExts", "verifyTypeScriptSetup", "verifyPartytownSetup", "getRouteRegex", "normalizeAppPath", "buildDataRoute", "getRouteMatcher", "normalizePathSep", "createClientRouterFilter", "absolutePathToPage", "generateInterceptionRoutesRewrites", "CLIENT_STATIC_FILES_PATH", "DEV_CLIENT_PAGES_MANIFEST", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "PHASE_DEVELOPMENT_SERVER", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "getMiddlewareRouteMatcher", "isMiddlewareFile", "NestedMiddlewareError", "isInstrumentationHookFile", "getPossibleMiddlewareFilenames", "getPossibleInstrumentationHookFilenames", "devPageFiles", "HMR_ACTIONS_SENT_TO_BROWSER", "PAGE_TYPES", "createHotReloaderTurbopack", "generateEncryptionKeyBase64", "isMetadataRouteFile", "normalizeMetadataPageToRoute", "createEnvDefinitions", "JsConfigPathsPlugin", "store", "consoleStore", "isPersistentCachingEnabled", "ModuleBuildError", "TurbopackInternalError", "verifyTypeScript", "opts", "usingTypeScript", "verifyResult", "dir", "distDir", "nextConfig", "intentDirs", "pagesDir", "appDir", "filter", "Boolean", "typeCheckPreflight", "tsconfigPath", "typescript", "disableStaticImages", "images", "hasAppDir", "hasPagesDir", "version", "propagateServerField", "field", "args", "renderServer", "instance", "startWatcher", "resetFetch", "useFileSystemPublicRoutes", "join", "distTypesDir", "existsSync", "recursive", "validFile<PERSON><PERSON><PERSON>", "pageExtensions", "serverFields", "setState", "logging", "hotReloader", "turbo", "config", "buildId", "<PERSON><PERSON><PERSON>", "isBuild", "telemetry", "rewrites", "fs<PERSON><PERSON><PERSON>", "previewProps", "prerenderManifest", "preview", "start", "experimental", "nextScriptWorkers", "ensure<PERSON><PERSON>back", "ensure", "item", "type", "ensurePage", "clientOnly", "page", "itemPath", "isApp", "definition", "undefined", "resolved", "prevSortedRoutes", "Promise", "resolve", "reject", "readdir", "_", "files", "length", "pages", "app", "directories", "rootDir", "nestedMiddleware", "envFiles", "map", "file", "push", "tsconfigPaths", "wp", "ignored", "pathname", "some", "startsWith", "d", "fileWatchTimes", "Map", "enabledTypeScript", "previousClientRouterFilters", "previousConflictingPagePaths", "Set", "on", "middlewareMatchers", "routedPages", "knownFiles", "getTimeInfoEntries", "appPaths", "pageNameSet", "conflictingAppPagePaths", "appPageFilePaths", "pagesPageFilePaths", "envChange", "tsconfigChange", "conflictingPageChange", "hasRootAppNotFound", "appFiles", "pageFiles", "clear", "sortedKnownFiles", "keys", "sort", "fileName", "includes", "meta", "get", "watchTime", "watchTimeChange", "timestamp", "set", "endsWith", "accuracy", "isPageFile", "isAppPath", "isPagePath", "rootFile", "extensions", "keepIndex", "pagesType", "ROOT", "staticInfo", "pageFilePath", "isDev", "isInsideAppDir", "output", "error", "actualMiddlewareFile", "middleware", "matchers", "regexp", "originalSource", "actualInstrumentationHookFile", "add", "pageName", "APP", "PAGES", "replace", "pageType", "generateSitemaps", "generateImageMetadata", "isRootNotFound", "isAppRouterPage", "originalPageName", "nextDataRoutes", "has", "test", "numConflicting", "size", "errorMessage", "p", "appPath", "relative", "pagesPath", "setHmrServerError", "Error", "clearHmrServerError", "clientRouterFilters", "clientRouterFilter", "Object", "clientRouterFilterRedirects", "_originalRedirects", "r", "internal", "clientRouterFilterAllowedRate", "JSON", "stringify", "then", "catch", "loadedEnvFiles", "process", "env", "NODE_ENV", "env<PERSON><PERSON><PERSON><PERSON>", "info", "typedEnv", "configFileName", "contents", "dev", "forceReload", "silent", "tsconfigResult", "turbopackProject", "hasRewrites", "afterFiles", "beforeFiles", "fallback", "update", "defineEnv", "isTurbopack", "fetchCacheKeyPrefix", "activeWebpackConfigs", "for<PERSON>ach", "idx", "isClient", "isNodeServer", "isEdgeServer", "plugins", "plugin", "jsConfig", "resolvedBaseUrl", "currentResolvedBaseUrl", "resolvedUrlIndex", "modules", "findIndex", "baseUrl", "splice", "isImplicit", "compilerOptions", "paths", "key", "assign", "definitions", "__NEXT_DEFINE_ENV", "newDefine", "isNodeOrEdgeCompilation", "invalidate", "reloadAfterInvalidation", "message", "appPathRoutes", "fromEntries", "entries", "k", "v", "match", "hasAppNotFound", "middlewareMatcher", "interceptionRoutes", "basePath", "caseSensitiveRoutes", "exportPathMap", "outDir", "exportPathMapEntries", "exportPathMapRoutes", "value", "source", "destination", "query", "sortedRoutes", "dynamicRoutes", "regex", "re", "toString", "dataRoutes", "route", "routeRegex", "i18n", "RegExp", "dataRouteRegex", "groups", "unshift", "every", "val", "addedRoutes", "removedRoutes", "send", "action", "DEV_PAGES_MANIFEST_UPDATE", "data", "devPagesManifest", "ADDED_PAGE", "REMOVED_PAGE", "e", "warn", "watch", "startTime", "clientPagesManifestPath", "devVirtualFsItems", "devMiddlewareManifestPath", "devTurbopackMiddlewareManifestPath", "requestHandler", "req", "res", "parsedUrl", "parse", "statusCode", "<PERSON><PERSON><PERSON><PERSON>", "end", "finished", "logErrorWithOriginalStack", "err", "ensureMiddleware", "requestUrl", "setupDevBundler", "isSrcDir", "result", "record", "webpackVersion", "turboFlag", "cliCommand", "isCustomServer", "has<PERSON>ow<PERSON><PERSON>", "cwd", "eventName", "payload", "featureName", "invocationCount"], "mappings": "AAGA,SACEA,iBAAiB,QAEZ,+CAA8C;AAKrD,SAASC,eAAe,QAAQ,qBAAoB;AACpD,OAAOC,QAAQ,KAAI;AACnB,SAASC,KAAK,QAAQ,cAAa;AACnC,OAAOC,SAAS,MAAK;AACrB,OAAOC,UAAU,OAAM;AACvB,OAAOC,QAAQ,cAAa;AAC5B,OAAOC,eAAe,+BAA8B;AACpD,SAASC,aAAa,QAAQ,YAAW;AACzC,OAAOC,YAAY,6BAA4B;AAC/C,SAASC,gBAAgB,QAAQ,eAAc;AAC/C,YAAYC,SAAS,4BAA2B;AAChD,OAAOC,wBAAwB,iCAAgC;AAC/D,SAASC,SAAS,QAAQ,wBAAuB;AAGjD,OAAOC,kBAAkB,+BAA8B;AACvD,SAASC,sBAAsB,QAAQ,oBAAmB;AAC1D,SACEC,yBAAyB,EACzBC,eAAe,QACV,4BAA2B;AAClC,SAASC,YAAY,QAAQ,mDAAkD;AAC/E,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SACEC,6BAA6B,EAC7BC,cAAc,QACT,yBAAwB;AAC/B,SAASC,qBAAqB,QAAQ,uCAAsC;AAC5E,SAASC,oBAAoB,QAAQ,sCAAqC;AAC1E,SAASC,aAAa,QAAQ,+CAA8C;AAC5E,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,eAAe,QAAQ,iDAAgD;AAChF,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,wBAAwB,QAAQ,2CAA0C;AACnF,SAASC,kBAAkB,QAAQ,sDAAqD;AACxF,SAASC,kCAAkC,QAAQ,qDAAoD;AAEvG,SACEC,wBAAwB,EACxBC,yBAAyB,EACzBC,8BAA8B,EAC9BC,wBAAwB,EACxBC,oCAAoC,QAC/B,gCAA+B;AAEtC,SAASC,yBAAyB,QAAQ,4DAA2D;AAErG,SACEC,gBAAgB,EAChBC,qBAAqB,EACrBC,yBAAyB,EACzBC,8BAA8B,EAC9BC,uCAAuC,QAClC,uBAAsB;AAC7B,SAASC,YAAY,QAAQ,0DAAyD;AAEtF,SAASC,2BAA2B,QAAQ,+BAA8B;AAC1E,SAASC,UAAU,QAAQ,0BAAyB;AACpD,SAASC,0BAA0B,QAAQ,mCAAkC;AAC7E,SAASC,2BAA2B,QAAQ,2CAA0C;AACtF,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,4BAA4B,QAAQ,2CAA0C;AACvF,SAASC,oBAAoB,QAAQ,yCAAwC;AAC7E,SAASC,mBAAmB,QAAQ,uDAAsD;AAC1F,SAASC,SAASC,YAAY,QAAQ,8BAA6B;AACnE,SACEC,0BAA0B,EAC1BC,gBAAgB,EAChBC,sBAAsB,QACjB,sCAAqC;AAsC5C,eAAeC,iBAAiBC,IAAe;IAC7C,IAAIC,kBAAkB;IACtB,MAAMC,eAAe,MAAMtC,sBAAsB;QAC/CuC,KAAKH,KAAKG,GAAG;QACbC,SAASJ,KAAKK,UAAU,CAACD,OAAO;QAChCE,YAAY;YAACN,KAAKO,QAAQ;YAAEP,KAAKQ,MAAM;SAAC,CAACC,MAAM,CAACC;QAChDC,oBAAoB;QACpBC,cAAcZ,KAAKK,UAAU,CAACQ,UAAU,CAACD,YAAY;QACrDE,qBAAqBd,KAAKK,UAAU,CAACU,MAAM,CAACD,mBAAmB;QAC/DE,WAAW,CAAC,CAAChB,KAAKQ,MAAM;QACxBS,aAAa,CAAC,CAACjB,KAAKO,QAAQ;IAC9B;IAEA,IAAIL,aAAagB,OAAO,EAAE;QACxBjB,kBAAkB;IACpB;IACA,OAAOA;AACT;AAEA,OAAO,eAAekB,qBACpBnB,IAAe,EACfoB,KAA8B,EAC9BC,IAAS;QAEHrB,6BAAAA;IAAN,QAAMA,qBAAAA,KAAKsB,YAAY,sBAAjBtB,8BAAAA,mBAAmBuB,QAAQ,qBAA3BvB,4BAA6BmB,oBAAoB,CAACnB,KAAKG,GAAG,EAAEiB,OAAOC;AAC3E;AAEA,eAAeG,aAAaxB,IAAe;IACzC,MAAM,EAAEK,UAAU,EAAEG,MAAM,EAAED,QAAQ,EAAEJ,GAAG,EAAEsB,UAAU,EAAE,GAAGzB;IAC1D,MAAM,EAAE0B,yBAAyB,EAAE,GAAGrB;IACtC,MAAMJ,kBAAkB,MAAMF,iBAAiBC;IAE/C,MAAMI,UAAUzD,KAAKgF,IAAI,CAAC3B,KAAKG,GAAG,EAAEH,KAAKK,UAAU,CAACD,OAAO;IAE3D,4CAA4C;IAC5C,IAAIH,iBAAiB;QACnB,MAAM2B,eAAejF,KAAKgF,IAAI,CAACvB,SAAS;QACxC,IAAI,CAAC5D,GAAGqF,UAAU,CAACD,eAAe;YAChC,MAAMnF,MAAMmF,cAAc;gBAAEE,WAAW;YAAK;QAC9C;IACF;IAEA3E,UAAU,WAAWiD;IACrBjD,UAAU,SAASsB;IAEnB,MAAMsD,mBAAmB1E,uBACvBgD,WAAW2B,cAAc,EACzBxB;IAGF,MAAMyB,eAA6B,CAAC;IAEpC,sEAAsE;IACtEtC,aAAauC,QAAQ,CAAC;QACpBC,SAAS9B,WAAW8B,OAAO,KAAK;IAClC;IAEA,MAAMC,cAA0CpC,KAAKqC,KAAK,GACtD,MAAMjD,2BAA2BY,MAAMiC,cAAc7B,SAASqB,cAC9D,IAAIvE,mBAAmB8C,KAAKG,GAAG,EAAE;QAC/BK;QACAD;QACAH;QACAkC,QAAQtC,KAAKK,UAAU;QACvBkC,SAAS;QACTC,eAAe,MAAMnD,4BAA4B;YAC/CoD,SAAS;YACTrC;QACF;QACAsC,WAAW1C,KAAK0C,SAAS;QACzBC,UAAU3C,KAAK4C,SAAS,CAACD,QAAQ;QACjCE,cAAc7C,KAAK4C,SAAS,CAACE,iBAAiB,CAACC,OAAO;QACtDtB;IACF;IAEJ,MAAMW,YAAYY,KAAK;IAEvB,IAAIhD,KAAKK,UAAU,CAAC4C,YAAY,CAACC,iBAAiB,EAAE;QAClD,MAAMrF,qBACJmC,KAAKG,GAAG,EACRxD,KAAKgF,IAAI,CAACvB,SAAS9B;IAEvB;IAEA0B,KAAK4C,SAAS,CAACO,cAAc,CAAC,eAAeC,OAAOC,IAAI;QACtD,IAAIA,KAAKC,IAAI,KAAK,aAAaD,KAAKC,IAAI,KAAK,YAAY;YACvD,MAAMlB,YAAYmB,UAAU,CAAC;gBAC3BC,YAAY;gBACZC,MAAMJ,KAAKK,QAAQ;gBACnBC,OAAON,KAAKC,IAAI,KAAK;gBACrBM,YAAYC;YACd;QACF;IACF;IAEA,IAAIC,WAAW;IACf,IAAIC,mBAA6B,EAAE;IAEnC,MAAM,IAAIC,QAAc,OAAOC,SAASC;QACtC,IAAI3D,UAAU;YACZ,yDAAyD;YACzD/D,GAAG2H,OAAO,CAAC5D,UAAU,CAAC6D,GAAGC;gBACvB,IAAIA,yBAAAA,MAAOC,MAAM,EAAE;oBACjB;gBACF;gBAEA,IAAI,CAACR,UAAU;oBACbG;oBACAH,WAAW;gBACb;YACF;QACF;QAEA,MAAMS,QAAQhE,WAAW;YAACA;SAAS,GAAG,EAAE;QACxC,MAAMiE,MAAMhE,SAAS;YAACA;SAAO,GAAG,EAAE;QAClC,MAAMiE,cAAc;eAAIF;eAAUC;SAAI;QAEtC,MAAME,UAAUnE,YAAYC;QAC5B,MAAM6D,QAAQ;eACTtF,+BACDpC,KAAKgF,IAAI,CAAC+C,SAAU,OACpBrE,WAAW2B,cAAc;eAExBhD,wCACDrC,KAAKgF,IAAI,CAAC+C,SAAU,OACpBrE,WAAW2B,cAAc;SAE5B;QACD,IAAI2C,mBAA6B,EAAE;QAEnC,MAAMC,WAAW;YACf;YACA;YACA;YACA;SACD,CAACC,GAAG,CAAC,CAACC,OAASnI,KAAKgF,IAAI,CAACxB,KAAK2E;QAE/BT,MAAMU,IAAI,IAAIH;QAEd,wCAAwC;QACxC,MAAMI,gBAAgB;YACpBrI,KAAKgF,IAAI,CAACxB,KAAK;YACfxD,KAAKgF,IAAI,CAACxB,KAAK;SAChB;QACDkE,MAAMU,IAAI,IAAIC;QAEd,MAAMC,KAAK,IAAIpI,UAAU;YACvBqI,SAAS,CAACC;gBACR,OACE,CAACd,MAAMe,IAAI,CAAC,CAACN,OAASA,KAAKO,UAAU,CAACF,cACtC,CAACV,YAAYW,IAAI,CACf,CAACE,IAAMH,SAASE,UAAU,CAACC,MAAMA,EAAED,UAAU,CAACF;YAGpD;QACF;QACA,MAAMI,iBAAiB,IAAIC;QAC3B,IAAIC,oBAAoBxF;QACxB,IAAIyF;QACJ,IAAIC,+BAA4C,IAAIC;QAEpDX,GAAGY,EAAE,CAAC,cAAc;gBA2diB5D,0BACLA;YA3d9B,IAAI6D;YACJ,MAAMC,cAAwB,EAAE;YAChC,MAAMC,aAAaf,GAAGgB,kBAAkB;YACxC,MAAMC,WAAqC,CAAC;YAC5C,MAAMC,cAAc,IAAIP;YACxB,MAAMQ,0BAA0B,IAAIR;YACpC,MAAMS,mBAAmB,IAAIb;YAC7B,MAAMc,qBAAqB,IAAId;YAE/B,IAAIe,YAAY;YAChB,IAAIC,iBAAiB;YACrB,IAAIC,wBAAwB;YAC5B,IAAIC,qBAAqB;YAEzB,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAG5G,KAAK4C,SAAS;YAE9C+D,SAASE,KAAK;YACdD,UAAUC,KAAK;YACf5H,aAAa4H,KAAK;YAElB,MAAMC,mBAA6B;mBAAId,WAAWe,IAAI;aAAG,CAACC,IAAI,CAC5DrJ,eAAe0C,WAAW2B,cAAc;YAG1C,KAAK,MAAMiF,YAAYH,iBAAkB;gBACvC,IACE,CAACzC,MAAM6C,QAAQ,CAACD,aAChB,CAACxC,YAAYW,IAAI,CAAC,CAACE,IAAM2B,SAAS5B,UAAU,CAACC,KAC7C;oBACA;gBACF;gBACA,MAAM6B,OAAOnB,WAAWoB,GAAG,CAACH;gBAE5B,MAAMI,YAAY9B,eAAe6B,GAAG,CAACH;gBACrC,gGAAgG;gBAChG,MAAMK,kBACJD,cAAcxD,aACbwD,aAAaA,eAAcF,wBAAAA,KAAMI,SAAS;gBAC7ChC,eAAeiC,GAAG,CAACP,UAAUE,wBAAAA,KAAMI,SAAS;gBAE5C,IAAI3C,SAASsC,QAAQ,CAACD,WAAW;oBAC/B,IAAIK,iBAAiB;wBACnBf,YAAY;oBACd;oBACA;gBACF;gBAEA,IAAIvB,cAAckC,QAAQ,CAACD,WAAW;oBACpC,IAAIA,SAASQ,QAAQ,CAAC,kBAAkB;wBACtChC,oBAAoB;oBACtB;oBACA,IAAI6B,iBAAiB;wBACnBd,iBAAiB;oBACnB;oBACA;gBACF;gBAEA,IACEW,CAAAA,wBAAAA,KAAMO,QAAQ,MAAK7D,aACnB,CAAC9B,iBAAiB4F,UAAU,CAACV,WAC7B;oBACA;gBACF;gBAEA,MAAMW,YAAYlH,QAChBF,UACEtC,iBAAiB+I,UAAU5B,UAAU,CACnCnH,iBAAiBsC,UAAU;gBAGjC,MAAMqH,aAAanH,QACjBH,YACErC,iBAAiB+I,UAAU5B,UAAU,CACnCnH,iBAAiBqC,YAAY;gBAInC,MAAMuH,WAAW1J,mBAAmB6I,UAAU;oBAC5C9G,KAAKA;oBACL4H,YAAY1H,WAAW2B,cAAc;oBACrCgG,WAAW;oBACXC,WAAW9I,WAAW+I,IAAI;gBAC5B;gBAEA,IAAItJ,iBAAiBkJ,WAAW;wBAsBTK;oBArBrB,MAAMA,aAAa,MAAMzK,8BAA8B;wBACrD0K,cAAcnB;wBACd3E,QAAQjC;wBACRG,QAAQA;wBACRiD,MAAMqE;wBACNO,OAAO;wBACPC,gBAAgBV;wBAChB5F,gBAAgB3B,WAAW2B,cAAc;oBAC3C;oBACA,IAAI3B,WAAWkI,MAAM,KAAK,UAAU;wBAClCtL,IAAIuL,KAAK,CACP;wBAEF;oBACF;oBACAvG,aAAawG,oBAAoB,GAAGX;oBACpC,MAAM3G,qBACJnB,MACA,wBACAiC,aAAawG,oBAAoB;oBAEnC3C,qBAAqBqC,EAAAA,yBAAAA,WAAWO,UAAU,qBAArBP,uBAAuBQ,QAAQ,KAAI;wBACtD;4BAAEC,QAAQ;4BAAMC,gBAAgB;wBAAU;qBAC3C;oBACD;gBACF;gBACA,IAAI/J,0BAA0BgJ,WAAW;oBACvC7F,aAAa6G,6BAA6B,GAAGhB;oBAC7C,MAAM3G,qBACJnB,MACA,iCACAiC,aAAa6G,6BAA6B;oBAE5C;gBACF;gBAEA,IAAI7B,SAASQ,QAAQ,CAAC,UAAUR,SAASQ,QAAQ,CAAC,SAAS;oBACzDhC,oBAAoB;gBACtB;gBAEA,IAAI,CAAEmC,CAAAA,aAAaC,UAAS,GAAI;oBAC9B;gBACF;gBAEA,yDAAyD;gBACzD5I,aAAa8J,GAAG,CAAC9B;gBAEjB,IAAI+B,WAAW5K,mBAAmB6I,UAAU;oBAC1C9G,KAAKyH,YAAYpH,SAAUD;oBAC3BwH,YAAY1H,WAAW2B,cAAc;oBACrCgG,WAAWJ;oBACXK,WAAWL,YAAYzI,WAAW8J,GAAG,GAAG9J,WAAW+J,KAAK;gBAC1D;gBAEA,IACEtB,aACApH,UACAlB,oBACE2H,SAASkC,OAAO,CAAC3I,QAAQ,KACzBH,WAAW2B,cAAc,EACzB,OAEF;oBACA,MAAMmG,aAAa,MAAM7L,kBAAkB;wBACzC8L,cAAcnB;wBACd5G,YAAY,CAAC;wBACboD,MAAMuF;wBACNX,OAAO;wBACPe,UAAUjK,WAAW8J,GAAG;oBAC1B;oBAEAD,WAAWzJ,6BACTyJ,UACA,CAAC,CAAEb,CAAAA,WAAWkB,gBAAgB,IAAIlB,WAAWmB,qBAAqB,AAAD;gBAErE;gBAEA,IACE,CAAC1B,aACDoB,SAAS3D,UAAU,CAAC,YACpBhF,WAAWkI,MAAM,KAAK,UACtB;oBACAtL,IAAIuL,KAAK,CACP;oBAEF;gBACF;gBAEA,IAAIZ,WAAW;oBACb,MAAM2B,iBAAiBxH,iBAAiBwH,cAAc,CAACtC;oBACvDP,qBAAqB;oBAErB,IAAI6C,gBAAgB;wBAClB;oBACF;oBACA,IAAI,CAACA,kBAAkB,CAACxH,iBAAiByH,eAAe,CAACvC,WAAW;wBAClE;oBACF;oBACA,kEAAkE;oBAClE,IAAI/I,iBAAiB8K,UAAU9B,QAAQ,CAAC,OAAO;wBAC7C;oBACF;oBAEA,MAAMuC,mBAAmBT;oBACzBA,WAAWjL,iBAAiBiL,UAAUG,OAAO,CAAC,QAAQ;oBACtD,IAAI,CAACjD,QAAQ,CAAC8C,SAAS,EAAE;wBACvB9C,QAAQ,CAAC8C,SAAS,GAAG,EAAE;oBACzB;oBACA9C,QAAQ,CAAC8C,SAAS,CAACjE,IAAI,CAAC0E;oBAExB,IAAI/H,2BAA2B;wBAC7BiF,SAASoC,GAAG,CAACC;oBACf;oBAEA,IAAIjD,YAAYmB,QAAQ,CAAC8B,WAAW;wBAClC;oBACF;gBACF,OAAO;oBACL,IAAItH,2BAA2B;wBAC7BkF,UAAUmC,GAAG,CAACC;wBACd,8DAA8D;wBAC9D,8DAA8D;wBAC9DhJ,KAAK4C,SAAS,CAAC8G,cAAc,CAACX,GAAG,CAACC;oBACpC;gBACF;;gBACEpB,CAAAA,YAAYvB,mBAAmBC,kBAAiB,EAAGkB,GAAG,CACtDwB,UACA/B;gBAGF,IAAIzG,UAAU2F,YAAYwD,GAAG,CAACX,WAAW;oBACvC5C,wBAAwB2C,GAAG,CAACC;gBAC9B,OAAO;oBACL7C,YAAY4C,GAAG,CAACC;gBAClB;gBAEA;;;SAGC,GACD,IAAI,sBAAsBY,IAAI,CAACZ,WAAW;oBACxCrE,iBAAiBI,IAAI,CAACiE;oBACtB;gBACF;gBAEAjD,YAAYhB,IAAI,CAACiE;YACnB;YAEA,MAAMa,iBAAiBzD,wBAAwB0D,IAAI;YACnDrD,wBAAwBoD,iBAAiBlE,6BAA6BmE,IAAI;YAE1E,IAAIrD,0BAA0B,GAAG;gBAC/B,IAAIoD,iBAAiB,GAAG;oBACtB,IAAIE,eAAe,CAAC,6BAA6B,EAC/CF,mBAAmB,IAAI,SAAS,SACjC,0DAA0D,CAAC;oBAE5D,KAAK,MAAMG,KAAK5D,wBAAyB;wBACvC,MAAM6D,UAAUtN,KAAKuN,QAAQ,CAAC/J,KAAKkG,iBAAiBe,GAAG,CAAC4C;wBACxD,MAAMG,YAAYxN,KAAKuN,QAAQ,CAAC/J,KAAKmG,mBAAmBc,GAAG,CAAC4C;wBAC5DD,gBAAgB,CAAC,GAAG,EAAEI,UAAU,KAAK,EAAEF,QAAQ,GAAG,CAAC;oBACrD;oBACA7H,YAAYgI,iBAAiB,CAAC,qBAAuB,CAAvB,IAAIC,MAAMN,eAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAsB;gBACtD,OAAO,IAAIF,mBAAmB,GAAG;oBAC/BzH,YAAYkI,mBAAmB;oBAC/B,MAAMnJ,qBAAqBnB,MAAM,kBAAkB6D;gBACrD;YACF;YAEA8B,+BAA+BS;YAE/B,IAAImE;YACJ,IAAIlK,WAAW4C,YAAY,CAACuH,kBAAkB,EAAE;gBAC9CD,sBAAsBpM,yBACpBsM,OAAO1D,IAAI,CAACb,WACZ7F,WAAW4C,YAAY,CAACyH,2BAA2B,GAC/C,AAAC,CAAA,AAACrK,WAAmBsK,kBAAkB,IAAI,EAAE,AAAD,EAAGlK,MAAM,CACnD,CAACmK,IAAW,CAACA,EAAEC,QAAQ,IAEzB,EAAE,EACNxK,WAAW4C,YAAY,CAAC6H,6BAA6B;gBAGvD,IACE,CAACpF,+BACDqF,KAAKC,SAAS,CAACtF,iCACbqF,KAAKC,SAAS,CAACT,sBACjB;oBACAhE,YAAY;oBACZb,8BAA8B6E;gBAChC;YACF;YAEA,IAAI,CAACtK,mBAAmBwF,mBAAmB;gBACzC,oDAAoD;gBACpD,+CAA+C;gBAC/C,MAAM1F,iBAAiBC,MACpBiL,IAAI,CAAC;oBACJzE,iBAAiB;gBACnB,GACC0E,KAAK,CAAC,KAAO;YAClB;YAEA,IAAI3E,aAAaC,gBAAgB;oBAiE/BpE;gBAhEA,IAAImE,WAAW;wBAWUlG;oBAVvB,MAAM,EAAE8K,cAAc,EAAE,GAAGrO,cACzBqD,KACAiL,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBrO,KACA,MACA,CAACsO;wBACCtO,IAAIuO,IAAI,CAAC,CAAC,YAAY,EAAED,aAAa;oBACvC;oBAGF,IAAItL,qBAAmBI,2BAAAA,WAAW4C,YAAY,qBAAvB5C,yBAAyBoL,QAAQ,GAAE;wBACxD,0DAA0D;wBAC1DjM,qBAAqB;4BACnBY;4BACA+K,gBAAgB;mCACXA;gCACH;oCACExO,MAAM0D,WAAWqL,cAAc;oCAC/BL,KAAKhL,WAAWgL,GAAG;oCACnBM,UAAU;gCACZ;6BACD;wBACH;oBACF;oBAEA,MAAMxK,qBAAqBnB,MAAM,iBAAiB;wBAChD;4BAAE4L,KAAK;4BAAMC,aAAa;4BAAMC,QAAQ;wBAAK;qBAC9C;gBACH;gBACA,IAAIC;gBAIJ,IAAIvF,gBAAgB;oBAClB,IAAI;wBACFuF,iBAAiB,MAAM3O,aAAa+C,KAAKE;oBAC3C,EAAE,OAAO+D,GAAG;oBACV,2EAA2E,GAC7E;gBACF;gBAEA,IAAIhC,YAAY4J,gBAAgB,EAAE;oBAChC,MAAMC,cACJjM,KAAK4C,SAAS,CAACD,QAAQ,CAACuJ,UAAU,CAAC5H,MAAM,GAAG,KAC5CtE,KAAK4C,SAAS,CAACD,QAAQ,CAACwJ,WAAW,CAAC7H,MAAM,GAAG,KAC7CtE,KAAK4C,SAAS,CAACD,QAAQ,CAACyJ,QAAQ,CAAC9H,MAAM,GAAG;oBAE5C,MAAMlC,YAAY4J,gBAAgB,CAACK,MAAM,CAAC;wBACxCC,WAAW/P,gBAAgB;4BACzBgQ,aAAa;4BACbhC;4BACAjI,QAAQjC;4BACRuL,KAAK;4BACLxL;4BACAoM,qBACExM,KAAKK,UAAU,CAAC4C,YAAY,CAACuJ,mBAAmB;4BAClDP;4BACA,kBAAkB;4BAClBnG,oBAAoBjC;wBACtB;oBACF;gBACF;iBAEAzB,oCAAAA,YAAYqK,oBAAoB,qBAAhCrK,kCAAkCsK,OAAO,CAAC,CAACpK,QAAQqK;oBACjD,MAAMC,WAAWD,QAAQ;oBACzB,MAAME,eAAeF,QAAQ;oBAC7B,MAAMG,eAAeH,QAAQ;oBAC7B,MAAMV,cACJjM,KAAK4C,SAAS,CAACD,QAAQ,CAACuJ,UAAU,CAAC5H,MAAM,GAAG,KAC5CtE,KAAK4C,SAAS,CAACD,QAAQ,CAACwJ,WAAW,CAAC7H,MAAM,GAAG,KAC7CtE,KAAK4C,SAAS,CAACD,QAAQ,CAACyJ,QAAQ,CAAC9H,MAAM,GAAG;oBAE5C,IAAIkC,gBAAgB;4BAClBlE,yBAAAA;yBAAAA,kBAAAA,OAAO2B,OAAO,sBAAd3B,0BAAAA,gBAAgByK,OAAO,qBAAvBzK,wBAAyBoK,OAAO,CAAC,CAACM;4BAChC,mDAAmD;4BACnD,kCAAkC;4BAClC,IAAIA,kBAAkBvN,uBAAuBsM,gBAAgB;oCAGlCzJ,yBAAAA,iBAqBrB2K;gCAvBJ,MAAM,EAAEC,eAAe,EAAED,QAAQ,EAAE,GAAGlB;gCACtC,MAAMoB,yBAAyBH,OAAOE,eAAe;gCACrD,MAAME,oBAAmB9K,kBAAAA,OAAO2B,OAAO,sBAAd3B,0BAAAA,gBAAgB+K,OAAO,qBAAvB/K,wBAAyBgL,SAAS,CACzD,CAACjK,OAASA,UAAS8J,0CAAAA,uBAAwBI,OAAO;gCAGpD,IAAIL,iBAAiB;oCACnB,IACEA,gBAAgBK,OAAO,MAAKJ,0CAAAA,uBAAwBI,OAAO,GAC3D;wCACA,qCAAqC;wCACrC,IAAIH,oBAAoBA,mBAAmB,CAAC,GAAG;gDAC7C9K,0BAAAA;6CAAAA,mBAAAA,OAAO2B,OAAO,sBAAd3B,2BAAAA,iBAAgB+K,OAAO,qBAAvB/K,yBAAyBkL,MAAM,CAACJ,kBAAkB;wCACpD;wCAEA,wEAAwE;wCACxE,mEAAmE;wCACnE,IAAI,CAACF,gBAAgBO,UAAU,EAAE;gDAC/BnL,0BAAAA;6CAAAA,mBAAAA,OAAO2B,OAAO,sBAAd3B,2BAAAA,iBAAgB+K,OAAO,qBAAvB/K,yBAAyByC,IAAI,CAACmI,gBAAgBK,OAAO;wCACvD;oCACF;gCACF;gCAEA,IAAIN,CAAAA,6BAAAA,4BAAAA,SAAUS,eAAe,qBAAzBT,0BAA2BU,KAAK,KAAIT,iBAAiB;oCACvDzC,OAAO1D,IAAI,CAACiG,OAAOW,KAAK,EAAEjB,OAAO,CAAC,CAACkB;wCACjC,OAAOZ,OAAOW,KAAK,CAACC,IAAI;oCAC1B;oCACAnD,OAAOoD,MAAM,CAACb,OAAOW,KAAK,EAAEV,SAASS,eAAe,CAACC,KAAK;oCAC1DX,OAAOE,eAAe,GAAGA;gCAC3B;4BACF;wBACF;oBACF;oBAEA,IAAI3G,WAAW;4BACbjE;yBAAAA,kBAAAA,OAAOyK,OAAO,qBAAdzK,gBAAgBoK,OAAO,CAAC,CAACM;4BACvB,qDAAqD;4BACrD,sCAAsC;4BACtC,IACEA,UACA,OAAOA,OAAOc,WAAW,KAAK,YAC9Bd,OAAOc,WAAW,CAACC,iBAAiB,EACpC;gCACA,MAAMC,YAAYxQ,aAAa;oCAC7B+O,aAAa;oCACbhC;oCACAjI,QAAQjC;oCACRuL,KAAK;oCACLxL;oCACAoM,qBACExM,KAAKK,UAAU,CAAC4C,YAAY,CAACuJ,mBAAmB;oCAClDP;oCACAW;oCACAE;oCACAmB,yBAAyBpB,gBAAgBC;oCACzCD;oCACA/G,oBAAoBjC;gCACtB;gCAEA4G,OAAO1D,IAAI,CAACiG,OAAOc,WAAW,EAAEpB,OAAO,CAAC,CAACkB;oCACvC,IAAI,CAAEA,CAAAA,OAAOI,SAAQ,GAAI;wCACvB,OAAOhB,OAAOc,WAAW,CAACF,IAAI;oCAChC;gCACF;gCACAnD,OAAOoD,MAAM,CAACb,OAAOc,WAAW,EAAEE;4BACpC;wBACF;oBACF;gBACF;gBACA,MAAM5L,YAAY8L,UAAU,CAAC;oBAC3BC,yBAAyB5H;gBAC3B;YACF;YAEA,IAAI5B,iBAAiBL,MAAM,GAAG,GAAG;gBAC/BrH,IAAIuL,KAAK,CACP,qBAIC,CAJD,IAAI3J,sBACF8F,kBACAxE,KACCI,YAAYC,SAHf,qBAAA;2BAAA;gCAAA;kCAAA;gBAIA,GAAE4N,OAAO;gBAEXzJ,mBAAmB,EAAE;YACvB;YAEA,sEAAsE;YACtE1C,aAAaoM,aAAa,GAAG5D,OAAO6D,WAAW,CAC7C7D,OAAO8D,OAAO,CAACrI,UAAUrB,GAAG,CAAC,CAAC,CAAC2J,GAAGC,EAAE,GAAK;oBAACD;oBAAGC,EAAEzH,IAAI;iBAAG;YAExD,MAAM7F,qBACJnB,MACA,iBACAiC,aAAaoM,aAAa;YAG5B,gDAAgD;YAChDpM,aAAayG,UAAU,GAAG5C,qBACtB;gBACE4I,OAAO;gBACPjL,MAAM;gBACNkF,UAAU7C;YACZ,IACAjC;YAEJ,MAAM1C,qBAAqBnB,MAAM,cAAciC,aAAayG,UAAU;YACtEzG,aAAa0M,cAAc,GAAGjI;YAE9B1G,KAAK4C,SAAS,CAACgM,iBAAiB,GAAG3M,EAAAA,2BAAAA,aAAayG,UAAU,qBAAvBzG,yBAAyB0G,QAAQ,IAChEhK,2BAA0BsD,4BAAAA,aAAayG,UAAU,qBAAvBzG,0BAAyB0G,QAAQ,IAC3D9E;YAEJ,MAAMgL,qBAAqBxQ,mCACzBoM,OAAO1D,IAAI,CAACb,WACZlG,KAAKK,UAAU,CAACyO,QAAQ,EACxBjK,GAAG,CAAC,CAACxB,OACLrG,iBACE,wBACAqG,MACArD,KAAKK,UAAU,CAACyO,QAAQ,EACxB9O,KAAKK,UAAU,CAAC4C,YAAY,CAAC8L,mBAAmB;YAIpD/O,KAAK4C,SAAS,CAACD,QAAQ,CAACwJ,WAAW,CAACpH,IAAI,IAAI8J;YAE5C,MAAMG,gBACJ,AAAC,OAAO3O,WAAW2O,aAAa,KAAK,cAClC,OAAM3O,WAAW2O,aAAa,oBAAxB3O,WAAW2O,aAAa,MAAxB3O,YACL,CAAC,GACD;gBACEuL,KAAK;gBACLzL,KAAKH,KAAKG,GAAG;gBACb8O,QAAQ;gBACR7O,SAASA;gBACTmC,SAAS;YACX,OAEJ,CAAC;YAEH,MAAM2M,uBAAuBzE,OAAO8D,OAAO,CAACS,iBAAiB,CAAC;YAE9D,IAAIE,qBAAqB5K,MAAM,GAAG,GAAG;gBACnCtE,KAAK4C,SAAS,CAACuM,mBAAmB,GAAGD,qBAAqBrK,GAAG,CAC3D,CAAC,CAAC+I,KAAKwB,MAAM,GACXpS,iBACE,wBACA;wBACEqS,QAAQzB;wBACR0B,aAAa,GAAGF,MAAM3L,IAAI,GACxB2L,MAAMG,KAAK,GAAG,MAAM,KACnB3S,GAAGoO,SAAS,CAACoE,MAAMG,KAAK,GAAG;oBAChC,GACAvP,KAAKK,UAAU,CAACyO,QAAQ,EACxB9O,KAAKK,UAAU,CAAC4C,YAAY,CAAC8L,mBAAmB;YAGxD;YAEA,IAAI;gBACF,gEAAgE;gBAChE,qEAAqE;gBACrE,kEAAkE;gBAClE,MAAMS,eAAe/R,gBAAgBsI;gBAErC/F,KAAK4C,SAAS,CAAC6M,aAAa,GAAGD,aAAa3K,GAAG,CAC7C,CAACpB;oBACC,MAAMiM,QAAQ5R,cAAc2F;oBAC5B,OAAO;wBACLiM,OAAOA,MAAMC,EAAE,CAACC,QAAQ;wBACxBlB,OAAOzQ,gBAAgByR;wBACvBjM;oBACF;gBACF;gBAGF,MAAMoM,aAAkD,EAAE;gBAE1D,KAAK,MAAMpM,QAAQ+L,aAAc;oBAC/B,MAAMM,QAAQ9R,eAAeyF,MAAM;oBACnC,MAAMsM,aAAajS,cAAcgS,MAAMrM,IAAI;oBAC3CoM,WAAW9K,IAAI,CAAC;wBACd,GAAG+K,KAAK;wBACRJ,OAAOK,WAAWJ,EAAE,CAACC,QAAQ;wBAC7BlB,OAAOzQ,gBAAgB;4BACrB,+DAA+D;4BAC/D,uCAAuC;4BACvC0R,IAAI3P,KAAKK,UAAU,CAAC2P,IAAI,GACpB,IAAIC,OACFH,MAAMI,cAAc,CAAC/G,OAAO,CAC1B,CAAC,aAAa,CAAC,EACf,CAAC,mCAAmC,CAAC,KAGzC,IAAI8G,OAAOH,MAAMI,cAAc;4BACnCC,QAAQJ,WAAWI,MAAM;wBAC3B;oBACF;gBACF;gBACAnQ,KAAK4C,SAAS,CAAC6M,aAAa,CAACW,OAAO,IAAIP;gBAExC,IAAI,EAAC9L,oCAAAA,iBAAkBsM,KAAK,CAAC,CAACC,KAAK3D,MAAQ2D,QAAQd,YAAY,CAAC7C,IAAI,IAAG;oBACrE,MAAM4D,cAAcf,aAAa/O,MAAM,CACrC,CAACqP,QAAU,CAAC/L,iBAAiBmD,QAAQ,CAAC4I;oBAExC,MAAMU,gBAAgBzM,iBAAiBtD,MAAM,CAC3C,CAACqP,QAAU,CAACN,aAAatI,QAAQ,CAAC4I;oBAGpC,8CAA8C;oBAC9C1N,YAAYqO,IAAI,CAAC;wBACfC,QAAQxR,4BAA4ByR,yBAAyB;wBAC7DC,MAAM;4BACJ;gCACEC,kBAAkB;4BACpB;yBACD;oBACH;oBAEAN,YAAY7D,OAAO,CAAC,CAACoD;wBACnB1N,YAAYqO,IAAI,CAAC;4BACfC,QAAQxR,4BAA4B4R,UAAU;4BAC9CF,MAAM;gCAACd;6BAAM;wBACf;oBACF;oBAEAU,cAAc9D,OAAO,CAAC,CAACoD;wBACrB1N,YAAYqO,IAAI,CAAC;4BACfC,QAAQxR,4BAA4B6R,YAAY;4BAChDH,MAAM;gCAACd;6BAAM;wBACf;oBACF;gBACF;gBACA/L,mBAAmByL;gBAEnB,IAAI,CAAC1L,UAAU;oBACbG;oBACAH,WAAW;gBACb;YACF,EAAE,OAAOkN,GAAG;gBACV,IAAI,CAAClN,UAAU;oBACbI,OAAO8M;oBACPlN,WAAW;gBACb,OAAO;oBACL7G,IAAIgU,IAAI,CAAC,oCAAoCD;gBAC/C;YACF,SAAU;gBACR,kEAAkE;gBAClE,4DAA4D;gBAC5D,MAAM7P,qBAAqBnB,MAAM,kBAAkB6D;YACrD;QACF;QAEAoB,GAAGiM,KAAK,CAAC;YAAEzM,aAAa;gBAACtE;aAAI;YAAEgR,WAAW;QAAE;IAC9C;IAEA,MAAMC,0BAA0B,CAAC,OAAO,EAAE9S,yBAAyB,aAAa,EAAEC,2BAA2B;IAC7GyB,KAAK4C,SAAS,CAACyO,iBAAiB,CAACtI,GAAG,CAACqI;IAErC,MAAME,4BAA4B,CAAC,OAAO,EAAEhT,yBAAyB,aAAa,EAAEE,gCAAgC;IACpHwB,KAAK4C,SAAS,CAACyO,iBAAiB,CAACtI,GAAG,CAACuI;IAErC,MAAMC,qCAAqC,CAAC,OAAO,EAAEjT,yBAAyB,aAAa,EAAEI,sCAAsC;IACnIsB,KAAK4C,SAAS,CAACyO,iBAAiB,CAACtI,GAAG,CAACwI;IAErC,eAAeC,eAAeC,GAAoB,EAAEC,GAAmB;YAGjEC,qBAcFA,sBACAA;QAjBF,MAAMA,YAAYjV,IAAIkV,KAAK,CAACH,IAAI/U,GAAG,IAAI;QAEvC,KAAIiV,sBAAAA,UAAUxM,QAAQ,qBAAlBwM,oBAAoBzK,QAAQ,CAACkK,0BAA0B;YACzDM,IAAIG,UAAU,GAAG;YACjBH,IAAII,SAAS,CAAC,gBAAgB;YAC9BJ,IAAIK,GAAG,CACLhH,KAAKC,SAAS,CAAC;gBACbzG,OAAOR,iBAAiBtD,MAAM,CAC5B,CAACqP,QAAU,CAAC9P,KAAK4C,SAAS,CAAC+D,QAAQ,CAACgD,GAAG,CAACmG;YAE5C;YAEF,OAAO;gBAAEkC,UAAU;YAAK;QAC1B;QAEA,IACEL,EAAAA,uBAAAA,UAAUxM,QAAQ,qBAAlBwM,qBAAoBzK,QAAQ,CAACoK,iCAC7BK,uBAAAA,UAAUxM,QAAQ,qBAAlBwM,qBAAoBzK,QAAQ,CAACqK,sCAC7B;gBAGuBtP;YAFvByP,IAAIG,UAAU,GAAG;YACjBH,IAAII,SAAS,CAAC,gBAAgB;YAC9BJ,IAAIK,GAAG,CAAChH,KAAKC,SAAS,CAAC/I,EAAAA,2BAAAA,aAAayG,UAAU,qBAAvBzG,yBAAyB0G,QAAQ,KAAI,EAAE;YAC9D,OAAO;gBAAEqJ,UAAU;YAAK;QAC1B;QACA,OAAO;YAAEA,UAAU;QAAM;IAC3B;IAEA,SAASC,0BACPC,GAAY,EACZ5O,IAAyE;QAEzE,IAAI4O,eAAerS,kBAAkB;YACnC,wDAAwD;YACxD5C,IAAIuL,KAAK,CAAC0J,IAAI9D,OAAO;QACvB,OAAO,IAAI8D,eAAepS,wBAAwB;QAChD,yEAAyE;QACzE,mEAAmE;QACrE,OAAO,IAAIwD,SAAS,WAAW;YAC7BrG,IAAIgU,IAAI,CAACiB;QACX,OAAO,IAAI5O,SAAS,WAAW;YAC7BrG,IAAIuL,KAAK,CAAC0J;QACZ,OAAO,IAAI5O,MAAM;YACfrG,IAAIuL,KAAK,CAAC,GAAGlF,KAAK,CAAC,CAAC,EAAE4O;QACxB,OAAO;YACLjV,IAAIuL,KAAK,CAAC0J;QACZ;IACF;IAEA,OAAO;QACLjQ;QACAG;QACAoP;QACAS;QAEA,MAAME,kBAAiBC,UAAmB;YACxC,IAAI,CAACnQ,aAAawG,oBAAoB,EAAE;YACxC,OAAOrG,YAAYmB,UAAU,CAAC;gBAC5BE,MAAMxB,aAAawG,oBAAoB;gBACvCjF,YAAY;gBACZI,YAAYC;gBACZnH,KAAK0V;YACP;QACF;IACF;AACF;AAEA,OAAO,eAAeC,gBAAgBrS,IAAe;IACnD,MAAMsS,WAAW3V,KACduN,QAAQ,CAAClK,KAAKG,GAAG,EAAEH,KAAKO,QAAQ,IAAIP,KAAKQ,MAAM,IAAI,IACnD6E,UAAU,CAAC;IAEd,MAAMkN,SAAS,MAAM/Q,aAAaxB;IAElCA,KAAK0C,SAAS,CAAC8P,MAAM,CACnBjV,gBACEZ,KAAKgF,IAAI,CAAC3B,KAAKG,GAAG,EAAEH,KAAKK,UAAU,CAACD,OAAO,GAC3CJ,KAAKK,UAAU,EACf;QACEoS,gBAAgB;QAChBH;QACAI,WAAW,CAAC,CAAC1S,KAAKqC,KAAK;QACvBsQ,YAAY;QACZnS,QAAQ,CAAC,CAACR,KAAKQ,MAAM;QACrBD,UAAU,CAAC,CAACP,KAAKO,QAAQ;QACzBqS,gBAAgB,CAAC,CAAC5S,KAAK4S,cAAc;QACrCC,YAAY,CAAC,CAAE,MAAM9V,OAAO,YAAY;YAAE+V,KAAK9S,KAAKG,GAAG;QAAC;IAC1D;IAIJ,4CAA4C;IAC5CH,KAAK0C,SAAS,CAAC8P,MAAM,CAAC;QACpBO,WAAWzV;QACX0V,SAAS;YACPC,aAAa;YACbC,iBAAiBtT,2BAA2BI,KAAKK,UAAU,IAAI,IAAI;QACrE;IACF;IAEA,OAAOkS;AACT;CAIA,2DAA2D"}
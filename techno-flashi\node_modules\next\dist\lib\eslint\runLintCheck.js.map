{"version": 3, "sources": ["../../../src/lib/eslint/runLintCheck.ts"], "sourcesContent": ["import { promises as fs, existsSync } from 'fs'\nimport { bold, cyan, red, underline, yellow } from '../picocolors'\nimport path from 'path'\n\nimport findUp from 'next/dist/compiled/find-up'\nimport semver from 'next/dist/compiled/semver'\nimport * as CommentJson from 'next/dist/compiled/comment-json'\n\nimport { formatResults } from './customFormatter'\nimport type { LintResult } from './customFormatter'\nimport { writeDefaultConfig } from './writeDefaultConfig'\nimport { hasEslintConfiguration } from './hasEslintConfiguration'\nimport { writeOutputFile } from './writeOutputFile'\n\nimport { findPagesDir } from '../find-pages-dir'\nimport { installDependencies } from '../install-dependencies'\nimport { hasNecessaryDependencies } from '../has-necessary-dependencies'\n\nimport * as Log from '../../build/output/log'\nimport type { EventLintCheckCompleted } from '../../telemetry/events/build'\nimport isError, { getProperError } from '../is-error'\nimport { getPkgManager } from '../helpers/get-pkg-manager'\nimport {\n  getESLintStrictValue,\n  getESLintPromptValues,\n} from './getESLintPromptValues'\n\ntype Config = {\n  plugins: string[]\n  rules: { [key: string]: Array<number | string> }\n}\n\n// 0 is off, 1 is warn, 2 is error. See https://eslint.org/docs/user-guide/configuring/rules#configuring-rules\nconst VALID_SEVERITY = ['off', 'warn', 'error'] as const\ntype Severity = (typeof VALID_SEVERITY)[number]\n\nfunction isValidSeverity(severity: string): severity is Severity {\n  return VALID_SEVERITY.includes(severity as Severity)\n}\n\nconst requiredPackages = [\n  { file: 'eslint', pkg: 'eslint', exportsRestrict: false },\n  {\n    file: 'eslint-config-next',\n    pkg: 'eslint-config-next',\n    exportsRestrict: false,\n  },\n]\n\nasync function cliPrompt(cwd: string): Promise<{ config?: any }> {\n  console.log(\n    bold(\n      `${cyan(\n        '?'\n      )} How would you like to configure ESLint? https://nextjs.org/docs/app/api-reference/config/eslint`\n    )\n  )\n\n  try {\n    const cliSelect = (\n      await Promise.resolve(require('next/dist/compiled/cli-select'))\n    ).default\n    const { value } = await cliSelect({\n      values: await getESLintPromptValues(cwd),\n      valueRenderer: (\n        {\n          title,\n          recommended,\n        }: { title: string; recommended?: boolean; config: any },\n        selected: boolean\n      ) => {\n        const name = selected ? bold(underline(cyan(title))) : title\n        return name + (recommended ? bold(yellow(' (recommended)')) : '')\n      },\n      selected: cyan('❯ '),\n      unselected: '  ',\n    })\n\n    return { config: value?.config ?? null }\n  } catch {\n    return { config: null }\n  }\n}\n\nasync function lint(\n  baseDir: string,\n  lintDirs: string[],\n  eslintrcFile: string | null,\n  pkgJsonPath: string | null,\n  {\n    lintDuringBuild = false,\n    eslintOptions = null,\n    reportErrorsOnly = false,\n    maxWarnings = -1,\n    formatter = null,\n    outputFile = null,\n  }: {\n    lintDuringBuild: boolean\n    eslintOptions: any\n    reportErrorsOnly: boolean\n    maxWarnings: number\n    formatter: string | null\n    outputFile: string | null\n  }\n): Promise<\n  | string\n  | null\n  | {\n      output: string | null\n      isError: boolean\n      eventInfo: EventLintCheckCompleted\n    }\n> {\n  try {\n    // Load ESLint after we're sure it exists:\n    const deps = await hasNecessaryDependencies(baseDir, requiredPackages)\n    const packageManager = getPkgManager(baseDir)\n\n    if (deps.missing.some((dep) => dep.pkg === 'eslint')) {\n      Log.error(\n        `ESLint must be installed${\n          lintDuringBuild ? ' in order to run during builds:' : ':'\n        } ${bold(\n          cyan(\n            (packageManager === 'yarn'\n              ? 'yarn add --dev'\n              : packageManager === 'pnpm'\n                ? 'pnpm install --save-dev'\n                : 'npm install --save-dev') + ' eslint'\n          )\n        )}`\n      )\n      return null\n    }\n\n    const mod = await Promise.resolve(require(deps.resolved.get('eslint')!))\n\n    // If V9 config was found, use flat config, or else use legacy.\n    const useFlatConfig = eslintrcFile\n      ? // eslintrcFile is absolute path\n        path.basename(eslintrcFile).startsWith('eslint.config.')\n      : false\n\n    let ESLint\n    // loadESLint is >= 8.57.0\n    // PR https://github.com/eslint/eslint/pull/18098\n    // Release https://github.com/eslint/eslint/releases/tag/v8.57.0\n    if ('loadESLint' in mod) {\n      // By default, configType is `flat`. If `useFlatConfig` is false, the return value is `LegacyESLint`.\n      // https://github.com/eslint/eslint/blob/1def4cdfab1f067c5089df8b36242cdf912b0eb6/lib/types/index.d.ts#L1609-L1613\n      ESLint = await mod.loadESLint({\n        useFlatConfig,\n      })\n    } else {\n      // eslint < 8.57.0, use legacy ESLint\n      ESLint = mod.ESLint\n    }\n\n    let eslintVersion = ESLint?.version ?? mod.CLIEngine?.version\n\n    if (!eslintVersion || semver.lt(eslintVersion, '7.0.0')) {\n      return `${red(\n        'error'\n      )} - Your project has an older version of ESLint installed${\n        eslintVersion ? ' (' + eslintVersion + ')' : ''\n      }. Please upgrade to ESLint version 7 or above`\n    }\n\n    let options: any = {\n      useEslintrc: true,\n      baseConfig: {},\n      errorOnUnmatchedPattern: false,\n      extensions: ['.js', '.jsx', '.ts', '.tsx'],\n      cache: true,\n      ...eslintOptions,\n    }\n\n    if (semver.gte(eslintVersion, '9.0.0') && useFlatConfig) {\n      for (const option of [\n        'useEslintrc',\n        'extensions',\n        'ignorePath',\n        'reportUnusedDisableDirectives',\n        'resolvePluginsRelativeTo',\n        'rulePaths',\n        'inlineConfig',\n        'maxWarnings',\n      ]) {\n        if (option in options) {\n          delete options[option]\n        }\n      }\n    }\n\n    let eslint = new ESLint(options)\n\n    let nextEslintPluginIsEnabled = false\n    const nextRulesEnabled = new Map<string, Severity>()\n\n    for (const configFile of [eslintrcFile, pkgJsonPath]) {\n      if (!configFile) continue\n\n      const completeConfig: Config | undefined =\n        await eslint.calculateConfigForFile(configFile)\n      if (!completeConfig) continue\n\n      const plugins = completeConfig.plugins\n\n      const hasNextPlugin =\n        // in ESLint < 9, `plugins` value is string[]\n        Array.isArray(plugins)\n          ? plugins.includes('@next/next')\n          : // in ESLint >= 9, `plugins` value is Record<string, unknown>\n            '@next/next' in plugins\n\n      if (hasNextPlugin) {\n        nextEslintPluginIsEnabled = true\n        for (const [name, [severity]] of Object.entries(completeConfig.rules)) {\n          if (!name.startsWith('@next/next/')) {\n            continue\n          }\n          if (\n            typeof severity === 'number' &&\n            severity >= 0 &&\n            severity < VALID_SEVERITY.length\n          ) {\n            nextRulesEnabled.set(name, VALID_SEVERITY[severity])\n          } else if (\n            typeof severity === 'string' &&\n            isValidSeverity(severity)\n          ) {\n            nextRulesEnabled.set(name, severity)\n          }\n        }\n        break\n      }\n    }\n\n    const pagesDir = findPagesDir(baseDir).pagesDir\n    const pagesDirRules = pagesDir ? ['@next/next/no-html-link-for-pages'] : []\n\n    if (nextEslintPluginIsEnabled) {\n      let updatedPagesDir = false\n\n      for (const rule of pagesDirRules) {\n        if (\n          !options.baseConfig!.rules?.[rule] &&\n          !options.baseConfig!.rules?.[\n            rule.replace('@next/next', '@next/babel-plugin-next')\n          ]\n        ) {\n          if (!options.baseConfig!.rules) {\n            options.baseConfig!.rules = {}\n          }\n          options.baseConfig!.rules[rule] = [1, pagesDir]\n          updatedPagesDir = true\n        }\n      }\n\n      if (updatedPagesDir) {\n        eslint = new ESLint(options)\n      }\n    } else {\n      Log.warn('')\n      Log.warn(\n        'The Next.js plugin was not detected in your ESLint configuration. See https://nextjs.org/docs/app/api-reference/config/eslint#migrating-existing-config'\n      )\n    }\n\n    const lintStart = process.hrtime()\n\n    let results = await eslint.lintFiles(lintDirs)\n    let selectedFormatter = null\n\n    if (options.fix) await ESLint.outputFixes(results)\n    if (reportErrorsOnly) results = await ESLint.getErrorResults(results) // Only return errors if --quiet flag is used\n\n    if (formatter) selectedFormatter = await eslint.loadFormatter(formatter)\n    const formattedResult = await formatResults(\n      baseDir,\n      results,\n      selectedFormatter?.format\n    )\n    const lintEnd = process.hrtime(lintStart)\n    const totalWarnings = results.reduce(\n      (sum: number, file: LintResult) => sum + file.warningCount,\n      0\n    )\n\n    if (outputFile) await writeOutputFile(outputFile, formattedResult.output)\n\n    return {\n      output: formattedResult.outputWithMessages,\n      isError:\n        ESLint.getErrorResults(results)?.length > 0 ||\n        (maxWarnings >= 0 && totalWarnings > maxWarnings),\n      eventInfo: {\n        durationInSeconds: lintEnd[0],\n        eslintVersion: eslintVersion,\n        lintedFilesCount: results.length,\n        lintFix: !!options.fix,\n        nextEslintPluginVersion:\n          nextEslintPluginIsEnabled && deps.resolved.has('eslint-config-next')\n            ? require(\n                path.join(\n                  path.dirname(deps.resolved.get('eslint-config-next')!),\n                  'package.json'\n                )\n              ).version\n            : null,\n        nextEslintPluginErrorsCount: formattedResult.totalNextPluginErrorCount,\n        nextEslintPluginWarningsCount:\n          formattedResult.totalNextPluginWarningCount,\n        nextRulesEnabled: Object.fromEntries(nextRulesEnabled),\n      },\n    }\n  } catch (err) {\n    if (lintDuringBuild) {\n      Log.error(\n        `ESLint: ${\n          isError(err) && err.message ? err.message.replace(/\\n/g, ' ') : err\n        }`\n      )\n      return null\n    } else {\n      throw getProperError(err)\n    }\n  }\n}\n\nexport async function runLintCheck(\n  baseDir: string,\n  lintDirs: string[],\n  opts: {\n    lintDuringBuild?: boolean\n    eslintOptions?: any\n    reportErrorsOnly?: boolean\n    maxWarnings?: number\n    formatter?: string | null\n    outputFile?: string | null\n    strict?: boolean\n  }\n): ReturnType<typeof lint> {\n  const {\n    lintDuringBuild = false,\n    eslintOptions = null,\n    reportErrorsOnly = false,\n    maxWarnings = -1,\n    formatter = null,\n    outputFile = null,\n    strict = false,\n  } = opts\n  try {\n    // Find user's .eslintrc file\n    // See: https://eslint.org/docs/user-guide/configuring/configuration-files#configuration-file-formats\n    const eslintrcFile =\n      (await findUp(\n        [\n          // eslint v9\n          'eslint.config.js',\n          'eslint.config.mjs',\n          'eslint.config.cjs',\n          // TS extensions require to install a separate package `jiti`.\n          // https://eslint.org/docs/latest/use/configure/configuration-files#typescript-configuration-files\n          'eslint.config.ts',\n          'eslint.config.mts',\n          'eslint.config.cts',\n          // eslint <= v8\n          '.eslintrc.js',\n          '.eslintrc.cjs',\n          '.eslintrc.yaml',\n          '.eslintrc.yml',\n          '.eslintrc.json',\n          '.eslintrc',\n        ],\n        {\n          cwd: baseDir,\n        }\n      )) ?? null\n\n    const pkgJsonPath = (await findUp('package.json', { cwd: baseDir })) ?? null\n    let packageJsonConfig = null\n    if (pkgJsonPath) {\n      const pkgJsonContent = await fs.readFile(pkgJsonPath, {\n        encoding: 'utf8',\n      })\n      packageJsonConfig = CommentJson.parse(pkgJsonContent)\n    }\n\n    const config = await hasEslintConfiguration(eslintrcFile, packageJsonConfig)\n    let deps\n\n    if (config.exists) {\n      // Run if ESLint config exists\n      return await lint(baseDir, lintDirs, eslintrcFile, pkgJsonPath, {\n        lintDuringBuild,\n        eslintOptions,\n        reportErrorsOnly,\n        maxWarnings,\n        formatter,\n        outputFile,\n      })\n    } else {\n      // Display warning if no ESLint configuration is present inside\n      // config file during \"next build\", no warning is shown when\n      // no eslintrc file is present\n      if (lintDuringBuild) {\n        if (config.emptyPkgJsonConfig || config.emptyEslintrc) {\n          Log.warn(\n            `No ESLint configuration detected. Run ${bold(\n              cyan('next lint')\n            )} to begin setup`\n          )\n        }\n        return null\n      } else {\n        // Ask user what config they would like to start with for first time \"next lint\" setup\n        const { config: selectedConfig } = strict\n          ? await getESLintStrictValue(baseDir)\n          : await cliPrompt(baseDir)\n\n        if (selectedConfig == null) {\n          // Show a warning if no option is selected in prompt\n          Log.warn(\n            'If you set up ESLint yourself, we recommend adding the Next.js ESLint plugin. See https://nextjs.org/docs/app/api-reference/config/eslint#migrating-existing-config'\n          )\n          return null\n        } else {\n          // Check if necessary deps installed, and install any that are missing\n          deps = await hasNecessaryDependencies(baseDir, requiredPackages)\n          if (deps.missing.length > 0) {\n            deps.missing.forEach((dep) => {\n              if (dep.pkg === 'eslint') {\n                // pin to v9 to avoid breaking changes\n                dep.pkg = 'eslint@^9'\n              }\n            })\n\n            await installDependencies(baseDir, deps.missing, true)\n          }\n\n          // Write default ESLint config.\n          // Check for /pages and src/pages is to make sure this happens in Next.js folder\n          if (\n            ['app', 'src/app', 'pages', 'src/pages'].some((dir) =>\n              existsSync(path.join(baseDir, dir))\n            )\n          ) {\n            await writeDefaultConfig(\n              baseDir,\n              config,\n              selectedConfig,\n              eslintrcFile,\n              pkgJsonPath,\n              packageJsonConfig\n            )\n          }\n        }\n\n        Log.ready(\n          `ESLint has successfully been configured. Run ${bold(\n            cyan('next lint')\n          )} again to view warnings and errors.`\n        )\n\n        return null\n      }\n    }\n  } catch (err) {\n    throw err\n  }\n}\n"], "names": ["runLintCheck", "VALID_SEVERITY", "isValidSeverity", "severity", "includes", "requiredPackages", "file", "pkg", "exportsRestrict", "cliPrompt", "cwd", "console", "log", "bold", "cyan", "cliSelect", "Promise", "resolve", "require", "default", "value", "values", "getESLintPromptValues", "valueR<PERSON><PERSON>", "title", "recommended", "selected", "name", "underline", "yellow", "unselected", "config", "lint", "baseDir", "lintDirs", "eslintrcFile", "pkgJsonPath", "lintDuringBuild", "eslintOptions", "reportErrorsOnly", "maxWarnings", "formatter", "outputFile", "mod", "ESLint", "deps", "hasNecessaryDependencies", "packageManager", "getPkgManager", "missing", "some", "dep", "Log", "error", "resolved", "get", "useFlatConfig", "path", "basename", "startsWith", "loadESLint", "eslintVersion", "version", "CLIEngine", "semver", "lt", "red", "options", "useEslintrc", "baseConfig", "errorOnUnmatchedPattern", "extensions", "cache", "gte", "option", "eslint", "nextEslintPluginIsEnabled", "nextRulesEnabled", "Map", "configFile", "completeConfig", "calculateConfigForFile", "plugins", "hasNextPlugin", "Array", "isArray", "Object", "entries", "rules", "length", "set", "pagesDir", "findPagesDir", "pagesDirRules", "updatedPagesDir", "rule", "replace", "warn", "lintStart", "process", "hrtime", "results", "lintFiles", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fix", "outputFixes", "getErrorResults", "loadFormatter", "formattedResult", "formatResults", "format", "lintEnd", "totalWarnings", "reduce", "sum", "warningCount", "writeOutputFile", "output", "outputWithMessages", "isError", "eventInfo", "durationInSeconds", "lintedFilesCount", "lintFix", "nextEslintPluginVersion", "has", "join", "dirname", "nextEslintPluginErrorsCount", "totalNextPluginErrorCount", "nextEslintPluginWarningsCount", "totalNextPluginWarningCount", "fromEntries", "err", "message", "getProperError", "opts", "strict", "findUp", "packageJsonConfig", "pkgJsonContent", "fs", "readFile", "encoding", "CommentJson", "parse", "hasEslintConfiguration", "exists", "emptyPkgJsonConfig", "emptyEslintrc", "selectedConfig", "getESLintStrictValue", "for<PERSON>ach", "installDependencies", "dir", "existsSync", "writeDefaultConfig", "ready"], "mappings": ";;;;+BA0UsBA;;;eAAAA;;;oBA1UqB;4BACQ;6DAClC;+DAEE;+DACA;qEACU;iCAEC;oCAEK;wCACI;iCACP;8BAEH;qCACO;0CACK;6DAEpB;iEAEmB;+BACV;uCAIvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOP,8GAA8G;AAC9G,MAAMC,iBAAiB;IAAC;IAAO;IAAQ;CAAQ;AAG/C,SAASC,gBAAgBC,QAAgB;IACvC,OAAOF,eAAeG,QAAQ,CAACD;AACjC;AAEA,MAAME,mBAAmB;IACvB;QAAEC,MAAM;QAAUC,KAAK;QAAUC,iBAAiB;IAAM;IACxD;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;CACD;AAED,eAAeC,UAAUC,GAAW;IAClCC,QAAQC,GAAG,CACTC,IAAAA,gBAAI,EACF,GAAGC,IAAAA,gBAAI,EACL,KACA,gGAAgG,CAAC;IAIvG,IAAI;QACF,MAAMC,YAAY,AAChB,CAAA,MAAMC,QAAQC,OAAO,CAACC,QAAQ,iCAAgC,EAC9DC,OAAO;QACT,MAAM,EAAEC,KAAK,EAAE,GAAG,MAAML,UAAU;YAChCM,QAAQ,MAAMC,IAAAA,4CAAqB,EAACZ;YACpCa,eAAe,CACb,EACEC,KAAK,EACLC,WAAW,EAC2C,EACxDC;gBAEA,MAAMC,OAAOD,WAAWb,IAAAA,gBAAI,EAACe,IAAAA,qBAAS,EAACd,IAAAA,gBAAI,EAACU,WAAWA;gBACvD,OAAOG,OAAQF,CAAAA,cAAcZ,IAAAA,gBAAI,EAACgB,IAAAA,kBAAM,EAAC,qBAAqB,EAAC;YACjE;YACAH,UAAUZ,IAAAA,gBAAI,EAAC;YACfgB,YAAY;QACd;QAEA,OAAO;YAAEC,QAAQX,CAAAA,yBAAAA,MAAOW,MAAM,KAAI;QAAK;IACzC,EAAE,OAAM;QACN,OAAO;YAAEA,QAAQ;QAAK;IACxB;AACF;AAEA,eAAeC,KACbC,OAAe,EACfC,QAAkB,EAClBC,YAA2B,EAC3BC,WAA0B,EAC1B,EACEC,kBAAkB,KAAK,EACvBC,gBAAgB,IAAI,EACpBC,mBAAmB,KAAK,EACxBC,cAAc,CAAC,CAAC,EAChBC,YAAY,IAAI,EAChBC,aAAa,IAAI,EAQlB;IAUD,IAAI;YA6CqCC,gBAwInCC;QApLJ,0CAA0C;QAC1C,MAAMC,OAAO,MAAMC,IAAAA,kDAAwB,EAACb,SAAS5B;QACrD,MAAM0C,iBAAiBC,IAAAA,4BAAa,EAACf;QAErC,IAAIY,KAAKI,OAAO,CAACC,IAAI,CAAC,CAACC,MAAQA,IAAI5C,GAAG,KAAK,WAAW;YACpD6C,KAAIC,KAAK,CACP,CAAC,wBAAwB,EACvBhB,kBAAkB,oCAAoC,IACvD,CAAC,EAAExB,IAAAA,gBAAI,EACNC,IAAAA,gBAAI,EACF,AAACiC,CAAAA,mBAAmB,SAChB,mBACAA,mBAAmB,SACjB,4BACA,wBAAuB,IAAK,aAEnC;YAEL,OAAO;QACT;QAEA,MAAMJ,MAAM,MAAM3B,QAAQC,OAAO,CAACC,QAAQ2B,KAAKS,QAAQ,CAACC,GAAG,CAAC;QAE5D,+DAA+D;QAC/D,MAAMC,gBAAgBrB,eAElBsB,aAAI,CAACC,QAAQ,CAACvB,cAAcwB,UAAU,CAAC,oBACvC;QAEJ,IAAIf;QACJ,0BAA0B;QAC1B,iDAAiD;QACjD,gEAAgE;QAChE,IAAI,gBAAgBD,KAAK;YACvB,qGAAqG;YACrG,kHAAkH;YAClHC,SAAS,MAAMD,IAAIiB,UAAU,CAAC;gBAC5BJ;YACF;QACF,OAAO;YACL,qCAAqC;YACrCZ,SAASD,IAAIC,MAAM;QACrB;QAEA,IAAIiB,gBAAgBjB,CAAAA,0BAAAA,OAAQkB,OAAO,OAAInB,iBAAAA,IAAIoB,SAAS,qBAAbpB,eAAemB,OAAO;QAE7D,IAAI,CAACD,iBAAiBG,eAAM,CAACC,EAAE,CAACJ,eAAe,UAAU;YACvD,OAAO,GAAGK,IAAAA,eAAG,EACX,SACA,wDAAwD,EACxDL,gBAAgB,OAAOA,gBAAgB,MAAM,GAC9C,6CAA6C,CAAC;QACjD;QAEA,IAAIM,UAAe;YACjBC,aAAa;YACbC,YAAY,CAAC;YACbC,yBAAyB;YACzBC,YAAY;gBAAC;gBAAO;gBAAQ;gBAAO;aAAO;YAC1CC,OAAO;YACP,GAAGlC,aAAa;QAClB;QAEA,IAAI0B,eAAM,CAACS,GAAG,CAACZ,eAAe,YAAYL,eAAe;YACvD,KAAK,MAAMkB,UAAU;gBACnB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,CAAE;gBACD,IAAIA,UAAUP,SAAS;oBACrB,OAAOA,OAAO,CAACO,OAAO;gBACxB;YACF;QACF;QAEA,IAAIC,SAAS,IAAI/B,OAAOuB;QAExB,IAAIS,4BAA4B;QAChC,MAAMC,mBAAmB,IAAIC;QAE7B,KAAK,MAAMC,cAAc;YAAC5C;YAAcC;SAAY,CAAE;YACpD,IAAI,CAAC2C,YAAY;YAEjB,MAAMC,iBACJ,MAAML,OAAOM,sBAAsB,CAACF;YACtC,IAAI,CAACC,gBAAgB;YAErB,MAAME,UAAUF,eAAeE,OAAO;YAEtC,MAAMC,gBACJ,6CAA6C;YAC7CC,MAAMC,OAAO,CAACH,WACVA,QAAQ9E,QAAQ,CAAC,gBAEjB,gBAAgB8E;YAEtB,IAAIC,eAAe;gBACjBP,4BAA4B;gBAC5B,KAAK,MAAM,CAACjD,MAAM,CAACxB,SAAS,CAAC,IAAImF,OAAOC,OAAO,CAACP,eAAeQ,KAAK,EAAG;oBACrE,IAAI,CAAC7D,KAAKgC,UAAU,CAAC,gBAAgB;wBACnC;oBACF;oBACA,IACE,OAAOxD,aAAa,YACpBA,YAAY,KACZA,WAAWF,eAAewF,MAAM,EAChC;wBACAZ,iBAAiBa,GAAG,CAAC/D,MAAM1B,cAAc,CAACE,SAAS;oBACrD,OAAO,IACL,OAAOA,aAAa,YACpBD,gBAAgBC,WAChB;wBACA0E,iBAAiBa,GAAG,CAAC/D,MAAMxB;oBAC7B;gBACF;gBACA;YACF;QACF;QAEA,MAAMwF,WAAWC,IAAAA,0BAAY,EAAC3D,SAAS0D,QAAQ;QAC/C,MAAME,gBAAgBF,WAAW;YAAC;SAAoC,GAAG,EAAE;QAE3E,IAAIf,2BAA2B;YAC7B,IAAIkB,kBAAkB;YAEtB,KAAK,MAAMC,QAAQF,cAAe;oBAE7B1B,2BACAA;gBAFH,IACE,GAACA,4BAAAA,QAAQE,UAAU,CAAEmB,KAAK,qBAAzBrB,yBAA2B,CAAC4B,KAAK,KAClC,GAAC5B,6BAAAA,QAAQE,UAAU,CAAEmB,KAAK,qBAAzBrB,0BAA2B,CAC1B4B,KAAKC,OAAO,CAAC,cAAc,2BAC5B,GACD;oBACA,IAAI,CAAC7B,QAAQE,UAAU,CAAEmB,KAAK,EAAE;wBAC9BrB,QAAQE,UAAU,CAAEmB,KAAK,GAAG,CAAC;oBAC/B;oBACArB,QAAQE,UAAU,CAAEmB,KAAK,CAACO,KAAK,GAAG;wBAAC;wBAAGJ;qBAAS;oBAC/CG,kBAAkB;gBACpB;YACF;YAEA,IAAIA,iBAAiB;gBACnBnB,SAAS,IAAI/B,OAAOuB;YACtB;QACF,OAAO;YACLf,KAAI6C,IAAI,CAAC;YACT7C,KAAI6C,IAAI,CACN;QAEJ;QAEA,MAAMC,YAAYC,QAAQC,MAAM;QAEhC,IAAIC,UAAU,MAAM1B,OAAO2B,SAAS,CAACpE;QACrC,IAAIqE,oBAAoB;QAExB,IAAIpC,QAAQqC,GAAG,EAAE,MAAM5D,OAAO6D,WAAW,CAACJ;QAC1C,IAAI9D,kBAAkB8D,UAAU,MAAMzD,OAAO8D,eAAe,CAACL,SAAS,6CAA6C;;QAEnH,IAAI5D,WAAW8D,oBAAoB,MAAM5B,OAAOgC,aAAa,CAAClE;QAC9D,MAAMmE,kBAAkB,MAAMC,IAAAA,8BAAa,EACzC5E,SACAoE,SACAE,qCAAAA,kBAAmBO,MAAM;QAE3B,MAAMC,UAAUZ,QAAQC,MAAM,CAACF;QAC/B,MAAMc,gBAAgBX,QAAQY,MAAM,CAClC,CAACC,KAAa5G,OAAqB4G,MAAM5G,KAAK6G,YAAY,EAC1D;QAGF,IAAIzE,YAAY,MAAM0E,IAAAA,gCAAe,EAAC1E,YAAYkE,gBAAgBS,MAAM;QAExE,OAAO;YACLA,QAAQT,gBAAgBU,kBAAkB;YAC1CC,SACE3E,EAAAA,0BAAAA,OAAO8D,eAAe,CAACL,6BAAvBzD,wBAAiC6C,MAAM,IAAG,KACzCjD,eAAe,KAAKwE,gBAAgBxE;YACvCgF,WAAW;gBACTC,mBAAmBV,OAAO,CAAC,EAAE;gBAC7BlD,eAAeA;gBACf6D,kBAAkBrB,QAAQZ,MAAM;gBAChCkC,SAAS,CAAC,CAACxD,QAAQqC,GAAG;gBACtBoB,yBACEhD,6BAA6B/B,KAAKS,QAAQ,CAACuE,GAAG,CAAC,wBAC3C3G,QACEuC,aAAI,CAACqE,IAAI,CACPrE,aAAI,CAACsE,OAAO,CAAClF,KAAKS,QAAQ,CAACC,GAAG,CAAC,wBAC/B,iBAEFO,OAAO,GACT;gBACNkE,6BAA6BpB,gBAAgBqB,yBAAyB;gBACtEC,+BACEtB,gBAAgBuB,2BAA2B;gBAC7CtD,kBAAkBS,OAAO8C,WAAW,CAACvD;YACvC;QACF;IACF,EAAE,OAAOwD,KAAK;QACZ,IAAIhG,iBAAiB;YACnBe,KAAIC,KAAK,CACP,CAAC,QAAQ,EACPkE,IAAAA,gBAAO,EAACc,QAAQA,IAAIC,OAAO,GAAGD,IAAIC,OAAO,CAACtC,OAAO,CAAC,OAAO,OAAOqC,KAChE;YAEJ,OAAO;QACT,OAAO;YACL,MAAME,IAAAA,uBAAc,EAACF;QACvB;IACF;AACF;AAEO,eAAerI,aACpBiC,OAAe,EACfC,QAAkB,EAClBsG,IAQC;IAED,MAAM,EACJnG,kBAAkB,KAAK,EACvBC,gBAAgB,IAAI,EACpBC,mBAAmB,KAAK,EACxBC,cAAc,CAAC,CAAC,EAChBC,YAAY,IAAI,EAChBC,aAAa,IAAI,EACjB+F,SAAS,KAAK,EACf,GAAGD;IACJ,IAAI;QACF,6BAA6B;QAC7B,qGAAqG;QACrG,MAAMrG,eACJ,AAAC,MAAMuG,IAAAA,eAAM,EACX;YACE,YAAY;YACZ;YACA;YACA;YACA,8DAA8D;YAC9D,kGAAkG;YAClG;YACA;YACA;YACA,eAAe;YACf;YACA;YACA;YACA;YACA;YACA;SACD,EACD;YACEhI,KAAKuB;QACP,MACI;QAER,MAAMG,cAAc,AAAC,MAAMsG,IAAAA,eAAM,EAAC,gBAAgB;YAAEhI,KAAKuB;QAAQ,MAAO;QACxE,IAAI0G,oBAAoB;QACxB,IAAIvG,aAAa;YACf,MAAMwG,iBAAiB,MAAMC,YAAE,CAACC,QAAQ,CAAC1G,aAAa;gBACpD2G,UAAU;YACZ;YACAJ,oBAAoBK,aAAYC,KAAK,CAACL;QACxC;QAEA,MAAM7G,SAAS,MAAMmH,IAAAA,8CAAsB,EAAC/G,cAAcwG;QAC1D,IAAI9F;QAEJ,IAAId,OAAOoH,MAAM,EAAE;YACjB,8BAA8B;YAC9B,OAAO,MAAMnH,KAAKC,SAASC,UAAUC,cAAcC,aAAa;gBAC9DC;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;YACF;QACF,OAAO;YACL,+DAA+D;YAC/D,4DAA4D;YAC5D,8BAA8B;YAC9B,IAAIL,iBAAiB;gBACnB,IAAIN,OAAOqH,kBAAkB,IAAIrH,OAAOsH,aAAa,EAAE;oBACrDjG,KAAI6C,IAAI,CACN,CAAC,sCAAsC,EAAEpF,IAAAA,gBAAI,EAC3CC,IAAAA,gBAAI,EAAC,cACL,eAAe,CAAC;gBAEtB;gBACA,OAAO;YACT,OAAO;gBACL,sFAAsF;gBACtF,MAAM,EAAEiB,QAAQuH,cAAc,EAAE,GAAGb,SAC/B,MAAMc,IAAAA,2CAAoB,EAACtH,WAC3B,MAAMxB,UAAUwB;gBAEpB,IAAIqH,kBAAkB,MAAM;oBAC1B,oDAAoD;oBACpDlG,KAAI6C,IAAI,CACN;oBAEF,OAAO;gBACT,OAAO;oBACL,sEAAsE;oBACtEpD,OAAO,MAAMC,IAAAA,kDAAwB,EAACb,SAAS5B;oBAC/C,IAAIwC,KAAKI,OAAO,CAACwC,MAAM,GAAG,GAAG;wBAC3B5C,KAAKI,OAAO,CAACuG,OAAO,CAAC,CAACrG;4BACpB,IAAIA,IAAI5C,GAAG,KAAK,UAAU;gCACxB,sCAAsC;gCACtC4C,IAAI5C,GAAG,GAAG;4BACZ;wBACF;wBAEA,MAAMkJ,IAAAA,wCAAmB,EAACxH,SAASY,KAAKI,OAAO,EAAE;oBACnD;oBAEA,+BAA+B;oBAC/B,gFAAgF;oBAChF,IACE;wBAAC;wBAAO;wBAAW;wBAAS;qBAAY,CAACC,IAAI,CAAC,CAACwG,MAC7CC,IAAAA,cAAU,EAAClG,aAAI,CAACqE,IAAI,CAAC7F,SAASyH,QAEhC;wBACA,MAAME,IAAAA,sCAAkB,EACtB3H,SACAF,QACAuH,gBACAnH,cACAC,aACAuG;oBAEJ;gBACF;gBAEAvF,KAAIyG,KAAK,CACP,CAAC,6CAA6C,EAAEhJ,IAAAA,gBAAI,EAClDC,IAAAA,gBAAI,EAAC,cACL,mCAAmC,CAAC;gBAGxC,OAAO;YACT;QACF;IACF,EAAE,OAAOuH,KAAK;QACZ,MAAMA;IACR;AACF"}
{"version": 3, "sources": ["../../../../src/lib/metadata/generate/utils.ts"], "sourcesContent": ["function resolveArray<T>(value: T | T[]): T[] {\n  if (Array.isArray(value)) {\n    return value as any\n  }\n  return [value] as any\n}\n\nfunction resolveAsArrayOrUndefined<T>(\n  value: T | T[] | undefined | null\n): T extends undefined | null ? undefined : T[] {\n  if (typeof value === 'undefined' || value === null) {\n    return undefined as any\n  }\n  return resolveArray(value) as any\n}\n\nfunction getOrigin(url: string | URL): string | undefined {\n  let origin = undefined\n  if (typeof url === 'string') {\n    try {\n      url = new URL(url)\n      origin = url.origin\n    } catch {}\n  }\n  return origin\n}\n\nexport { resolveAsArrayOrUndefined, resolveArray, getOrigin }\n"], "names": ["resolveArray", "value", "Array", "isArray", "resolveAsArrayOrUndefined", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "url", "origin", "URL"], "mappings": "AAAA,SAASA,aAAgBC,KAAc;IACrC,IAAIC,MAAMC,OAAO,CAACF,QAAQ;QACxB,OAAOA;IACT;IACA,OAAO;QAACA;KAAM;AAChB;AAEA,SAASG,0BACPH,KAAiC;IAEjC,IAAI,OAAOA,UAAU,eAAeA,UAAU,MAAM;QAClD,OAAOI;IACT;IACA,OAAOL,aAAaC;AACtB;AAEA,SAASK,UAAUC,GAAiB;IAClC,IAAIC,SAASH;IACb,IAAI,OAAOE,QAAQ,UAAU;QAC3B,IAAI;YACFA,MAAM,IAAIE,IAAIF;YACdC,SAASD,IAAIC,MAAM;QACrB,EAAE,OAAM,CAAC;IACX;IACA,OAAOA;AACT;AAEA,SAASJ,yBAAyB,EAAEJ,YAAY,EAAEM,SAAS,GAAE"}
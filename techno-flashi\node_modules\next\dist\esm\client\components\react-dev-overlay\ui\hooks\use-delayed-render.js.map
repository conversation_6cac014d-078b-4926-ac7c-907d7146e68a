{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/hooks/use-delayed-render.ts"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react'\n\ninterface Options {\n  enterDelay?: number\n  exitDelay?: number\n  onUnmount?: () => void\n}\n\n/**\n * Useful to perform CSS transitions on React components without\n * using libraries like Framer Motion. This hook will defer the\n * unmount of a React component until after a delay.\n *\n * @param active - Whether the component should be rendered\n * @param options - Options for the delayed render\n * @param options.enterDelay - Delay before rendering the component\n * @param options.exitDelay - Delay before unmounting the component\n *\n * const Modal = ({ active }) => {\n * const { mounted, rendered } = useDelayedRender(active, {\n *  exitDelay: 2000,\n * })\n *\n * if (!mounted) return null\n *\n * return (\n *   <Portal>\n *     <div className={rendered ? 'modal visible' : 'modal'}>...</div>\n *   </Portal>\n * )\n *}\n *\n * */\nexport function useDelayedRender(active = false, options: Options = {}) {\n  const [mounted, setMounted] = useState(active)\n  const [rendered, setRendered] = useState(false)\n  const renderTimerRef = useRef<number | null>(null)\n  const unmountTimerRef = useRef<number | null>(null)\n\n  const clearTimers = useCallback(() => {\n    if (renderTimerRef.current !== null) {\n      window.clearTimeout(renderTimerRef.current)\n      renderTimerRef.current = null\n    }\n    if (unmountTimerRef.current !== null) {\n      window.clearTimeout(unmountTimerRef.current)\n      unmountTimerRef.current = null\n    }\n  }, [])\n\n  useEffect(() => {\n    const { enterDelay = 1, exitDelay = 0 } = options\n\n    clearTimers()\n\n    if (active) {\n      setMounted(true)\n      if (enterDelay <= 0) {\n        setRendered(true)\n      } else {\n        renderTimerRef.current = window.setTimeout(() => {\n          setRendered(true)\n        }, enterDelay)\n      }\n    } else {\n      setRendered(false)\n      if (exitDelay <= 0) {\n        setMounted(false)\n      } else {\n        unmountTimerRef.current = window.setTimeout(() => {\n          setMounted(false)\n        }, exitDelay)\n      }\n    }\n\n    return clearTimers\n  }, [active, options, clearTimers])\n\n  return { mounted, rendered }\n}\n"], "names": ["useState", "useRef", "useCallback", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "active", "options", "mounted", "setMounted", "rendered", "setRendered", "renderTimerRef", "unmountTimerRef", "clearTimers", "current", "window", "clearTimeout", "enterDelay", "exitDelay", "setTimeout"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,QAAO;AAQhE;;;;;;;;;;;;;;;;;;;;;;;;GAwBG,GACH,OAAO,SAASC,iBAAiBC,MAAc,EAAEC,OAAqB;IAArCD,IAAAA,mBAAAA,SAAS;IAAOC,IAAAA,oBAAAA,UAAmB,CAAC;IACnE,MAAM,CAACC,SAASC,WAAW,GAAGR,SAASK;IACvC,MAAM,CAACI,UAAUC,YAAY,GAAGV,SAAS;IACzC,MAAMW,iBAAiBV,OAAsB;IAC7C,MAAMW,kBAAkBX,OAAsB;IAE9C,MAAMY,cAAcX,YAAY;QAC9B,IAAIS,eAAeG,OAAO,KAAK,MAAM;YACnCC,OAAOC,YAAY,CAACL,eAAeG,OAAO;YAC1CH,eAAeG,OAAO,GAAG;QAC3B;QACA,IAAIF,gBAAgBE,OAAO,KAAK,MAAM;YACpCC,OAAOC,YAAY,CAACJ,gBAAgBE,OAAO;YAC3CF,gBAAgBE,OAAO,GAAG;QAC5B;IACF,GAAG,EAAE;IAELX,UAAU;QACR,MAAM,EAAEc,aAAa,CAAC,EAAEC,YAAY,CAAC,EAAE,GAAGZ;QAE1CO;QAEA,IAAIR,QAAQ;YACVG,WAAW;YACX,IAAIS,cAAc,GAAG;gBACnBP,YAAY;YACd,OAAO;gBACLC,eAAeG,OAAO,GAAGC,OAAOI,UAAU,CAAC;oBACzCT,YAAY;gBACd,GAAGO;YACL;QACF,OAAO;YACLP,YAAY;YACZ,IAAIQ,aAAa,GAAG;gBAClBV,WAAW;YACb,OAAO;gBACLI,gBAAgBE,OAAO,GAAGC,OAAOI,UAAU,CAAC;oBAC1CX,WAAW;gBACb,GAAGU;YACL;QACF;QAEA,OAAOL;IACT,GAAG;QAACR;QAAQC;QAASO;KAAY;IAEjC,OAAO;QAAEN;QAASE;IAAS;AAC7B"}